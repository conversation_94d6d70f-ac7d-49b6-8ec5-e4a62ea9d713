<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.manulife.hk.ifp.nb.core</groupId>
	<artifactId>hk-ifp-nb-core-service-4</artifactId>
	<version>04.2025.06.14-SNAPSHOT</version>
	<packaging>jar</packaging>
	<name>hk-ifp-nb-core-service</name>
	<url>https://mfc.sharepoint.com/sites/asiatemp/asiadigital/DE/Microservices/Home.aspx</url>
	<description>HK IFP NB Core Service</description>

	<!--IFP_21_RL04_SB_NB_19968 start -->
	<!-- Identifies parent RSF pom -->
	<parent>
		<groupId>com.manulife.ap</groupId>
		<artifactId>rsf-parent</artifactId>
		<!--IFP_24_RL01_NBUW_11658 start-->
		<!--		<version>1.3.0</version>-->
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--<version>2.0.0.SR1</version>-->
		<version>2.2.5</version>
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<!--IFP_24_RL01_NBUW_11658 end-->
	</parent>
	<!--IFP_21_RL04_SB_NB_19968 end -->

	<!-- Properties -->
	<properties>
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--<maven.compiler.source>17</maven.compiler.source>
		<maven.compiler.target>17</maven.compiler.target>-->
		<java.version>21</java.version>
		<maven.compiler.source>21</maven.compiler.source>
		<maven.compiler.target>21</maven.compiler.target>
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<team.name>IFP_NB_CLAIMS</team.name>
		<team.your.name>NBUW Pillar</team.your.name>
		<team.your.email><EMAIL></team.your.email>
		<team.url>https://git.ap.manulife.com</team.url>
		<team.email><EMAIL></team.email>
		<team.zone>HK</team.zone>
		<sonar.exclusions>
			**/Application.java,
			**/swagger/*.java,
			**/swagger/**/*.java,
			**/config/**,
			**/key/**,
			**/view/**,
			**/dto/**,
			**/entity/**,
			<!-- IFP_23_RL02_NBUW_6947 added start -->
			**/pe/**,
			**/properties/**,
			**/*Constant.java,
			**/*Attribute.java,
			**/*Option.java,
			<!-- IFP_23_RL02_NBUW_6947 added end -->
			**/*Const.java,
			<!-- IFP_21_RL05_SB_NB_22268 start -->
			**/*Publisher.java
			**/*Constant.java,
			<!-- IFP_21_RL05_SB_NB_22268  end -->
			**/*Util.java,
			**/DataSourceConfiguration.java,
			**/CommandDataSourceProvider.java,
			**/QueryDataSourceProvider.java,
			**/ReflectionUtil.java,
			<!--IFP_21_RL04_SB_NB_19968 start -->
			**/*Test.java,
			<!--IFP_21_RL04_SB_NB_19968 end -->
			**/*VO.java,
			**/*Exception.java,
			**/**DTO.java,
			**/**Dto.java,
			**/**Request.java,
			**/**Response.java,
			**/**Enum.java,
			**/**Mapping.java,
			**/**Item.java,
			**/**Validator.java
		</sonar.exclusions>
		<start-class>com.manulife.hk.ifp.nb.core.Application</start-class>
		<masterBranchName>master</masterBranchName>
		<developBranchName>develop</developBranchName>
		<scmCommentPrefix>[RELEASE]</scmCommentPrefix>
		<apache-poi-version>3.15</apache-poi-version>
		<!--IFP_24_RL02_NBUW_16881 start -->
        <!--<org.json.version>20230227</org.json.version>-->
		<org.json.version>20231013</org.json.version>
		<!--IFP_24_RL02_NBUW_16881 end -->
		<springfox.version>2.9.2</springfox.version>
		<pojo-tester.version>0.7.6</pojo-tester.version>
		<!-- <flyway.version>5.2.4</flyway.version> --> <!-- 20240623 IFP_24_RL03_NBUW_23992 commented -->
		<!--IFP_21_RL04_SB_NB_19968 start -->
		<skip.unit.tests>false</skip.unit.tests>
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--<jacoco.version>0.8.8</jacoco.version>-->
		<jacoco.version>0.8.11</jacoco.version>
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<swagger-codegen-maven-plugin.version>2.4.20</swagger-codegen-maven-plugin.version>
		<!--IFP_21_RL04_SB_NB_19968 end -->
		<rsf.version>2.2.5</rsf.version>    <!--IFP_25_RL02_NBUW_35534 added  -->
		<!--IFP_22_RL05_NBUW_328 added start-->
		<docker.image.prefix>artifactory.ap.manulife.com/docker/</docker.image.prefix>
		<dockerfile-maven-plugin.version>1.4.12</dockerfile-maven-plugin.version>
		<dockerfile.skip>false</dockerfile.skip>
		<rsf.compliance.publish>false</rsf.compliance.publish>
		<scm.projectId>RSF</scm.projectId>
		<!--IFP_22_RL05_NBUW_328 added end-->
		<argLine>
			--add-exports=java.naming/com.sun.jndi.ldap=ALL-UNNAMED
			--add-opens=java.base/java.lang=ALL-UNNAMED
			--add-opens=java.base/java.lang.invoke=ALL-UNNAMED
			--add-opens=java.base/java.io=ALL-UNNAMED
			--add-opens=java.base/java.nio=ALL-UNNAMED
			--add-opens=java.base/java.nio.charset=ALL-UNNAMED
			--add-opens=java.base/java.security=ALL-UNNAMED
			--add-opens=java.base/java.util=ALL-UNNAMED
			--add-opens=java.base/java.time=ALL-UNNAMED
			--add-opens=java.management/javax.management=ALL-UNNAMED
			--add-opens=java.naming/javax.naming=ALL-UNNAMED
		</argLine>

	</properties>

	<!--IFP_22_RL05_NBUW_328 added start-->
	<scm>
		<url>http://git.ap.manulife.com/${scm.projectId}/${project.artifactId}.git</url>
		<connection>scm:git:https://git.ap.manulife.com/${scm.projectId}/${project.artifactId}.git</connection>
		<developerConnection>scm:git:https://git.ap.manulife.com/${scm.projectId}/${project.artifactId}.git</developerConnection>
	</scm>
	<!--IFP_22_RL05_NBUW_328 added end-->

	<!-- Organization: Add your organization details here -->
	<organization>
		<name>${team.name}</name>
		<url>${team.url}</url>
	</organization>

	<!-- Developers: Change to your team name, id and emails. etc... -->
	<developers>
		<developer>
			<id>your-name-id</id>
			<name>${team.your.name}</name>
			<email>${team.your.email}</email>
			<roles>
				<role>developer</role>
			</roles>
			<timezone>${team.zone}</timezone>
		</developer>
	</developers>
	<!-- Contributors -->
	<contributors>
		<contributor>
			<name>${team.your.name}-id</name>
			<email>${team.your.email}</email>
			<url>${project.url}</url>
			<roles>
				<role>architect</role>
				<role>developer</role>
			</roles>
			<timezone>${team.zone}</timezone>
		</contributor>
	</contributors>

	<!-- Deploy repositories -->
	<distributionManagement>
		<repository>
			<id>releases</id>
			<url>https://artifactory.ap.manulife.com/artifactory/libs-release-local</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<url>https://artifactory.ap.manulife.com/artifactory/libs-snapshot-local</url>
		</snapshotRepository>
	</distributionManagement>

	<!-- Project dependencies -->
	<dependencies>
		<!--IFP_21_RL04_SB_NB_19968 start -->
		<!-- RSF CORE -->
		<dependency>
			<groupId>com.manulife.ap</groupId>
			<artifactId>rsf-core</artifactId>
			<!--IFP_24_RL01_NBUW_11658 start-->
			<!--<version>1.3.0</version>-->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>2.0.0</version>-->
			<version>${rsf.version}</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<!--IFP_24_RL01_NBUW_11658 end-->
		</dependency>

		<!-- 20220531 IFP_23_RL05_NBUW_9907 sync issue start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>org.springframework.security</groupId>-->
		<!--	<artifactId>spring-security-config</artifactId>-->
		<!--	<version>5.6.12</version>-->
		<!--</dependency>-->

		<!--<dependency>-->
		<!--	<groupId>org.springframework</groupId>-->
		<!--	<artifactId>spring-webmvc</artifactId>-->
		<!--	<version>5.3.26</version>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!-- 20220531 IFP_23_RL05_NBUW_9907 sync issue end -->

		<!-- IFP_22_RL05_NBUW_328 modified start -->
		<!--		<dependency>-->
		<!--			<groupId>com.manulife.ap</groupId>-->
		<!--			<artifactId>rsf-pivotal</artifactId>-->
		<!--			<version>1.0.0</version>-->
		<!--			<type>pom</type>-->
		<!--		</dependency>-->
		<!-- IFP_22_RL05_NBUW_328 modified start -->
		<!--IFP_21_RL04_SB_NB_19968 end -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>javax.validation</groupId>-->
		<!--	<artifactId>validation-api</artifactId>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!-- Oauth2.0 -->
		<!-- IFP_22_RL05_NBUW_328 modified start -->
		<!--		<dependency>-->
		<!--			<groupId>com.manulife.common</groupId>-->
		<!--			<artifactId>common-service-util</artifactId>-->
		<!--			<version>1.0.0</version>-->
		<!--		</dependency>-->
		<!-- IFP_22_RL05_NBUW_328 modified end -->
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-config</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>6.1.4</version>-->
			<version>6.3.5</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!-- IFP_25_RL02_NBUW_35534 modified start-->
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.14.0</version>
		</dependency>
		<!-- IFP_25_RL02_NBUW_35534 modified end-->
		<!-- IFP_25_RL03_NBUW_43543 modified start -->
		<!-- <dependency> -->
		<!-- <groupId>io.projectreactor.netty</groupId> -->
        <!-- <artifactId>reactor-netty-http</artifactId> -->
        <!-- <version>1.0.39</version> -->
        <!-- </dependency> -->
    <!-- IFP_25_RL03_NBUW_43543 modified end -->
		<!--IFP_24_RL05_CRR_1240 start-->
<!--		<dependency>-->
<!--			<groupId>org.springframework.boot</groupId>-->
<!--			<artifactId>spring-boot-starter</artifactId>-->
<!--			<exclusions>-->
<!--				<exclusion>-->
<!--					<groupId>ch.qos.logback</groupId>-->
<!--					<artifactId>logback-classic</artifactId>-->
<!--				</exclusion>-->
<!--			</exclusions>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>ch.qos.logback</groupId>-->
<!--			<artifactId>logback-classic</artifactId>-->
<!--			<version>1.4.12</version>-->
<!--		</dependency>-->
<!--		<dependency>-->
<!--			<groupId>ch.qos.logback</groupId>-->
<!--			<artifactId>logback-core</artifactId>-->
<!--			<version>1.4.12</version>-->
<!--		</dependency>-->
		<!--IFP_24_RL05_CRR_1240 end-->
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!--IFP_21_RL04_SB_NB_19968 start -->
		<dependency>
			<groupId>org.springframework.security.oauth</groupId>
			<artifactId>spring-security-oauth2</artifactId>
			<!--IFP_23_RL02_NBUW_7689 start -->
			<version>2.5.2.RELEASE</version>
			<!--IFP_23_RL02_NBUW_7689 end -->
			<scope>compile</scope>
			<!--IFP_23_RL02_NBUW_7689 start -->
			<exclusions>
				<exclusion>
					<groupId>org.springframework</groupId>
					<artifactId>spring-expression</artifactId>
				</exclusion>
				<!--IFP_24_RL02_NBUW_16881 start -->
				<exclusion>
					<artifactId>spring-security-config</artifactId>
					<groupId>org.springframework.security</groupId>
				</exclusion>
				<!--IFP_24_RL02_NBUW_16881 end -->
			</exclusions>
			<!--IFP_23_RL02_NBUW_7689 end -->
		</dependency>

        <!-- 20240118 IFP_24_RL02_NBUW_9905 start -->
		<dependency>
			<groupId>org.reflections</groupId>
			<artifactId>reflections</artifactId>
			<version>0.10.2</version>
		</dependency>
        <!-- 20240118 IFP_24_RL02_NBUW_9905 end -->

		<!--IFP_23_RL02_NBUW_7689 start -->
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<!--IFP_24_RL04_NBUW_26288 start -->
			<!--<version>5.3.27</version> -->
			<version>5.3.39</version>
			<!--IFP_24_RL04_NBUW_end start -->
		</dependency>
		<!--IFP_23_RL02_NBUW_7689 end -->

		<!--IFP_21_RL04_SB_NB_19968 end -->
		<!-- End Oauth2.0 -->
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>org.apache.tomcat.embed</groupId>
			<artifactId>tomcat-embed-core</artifactId>
			<!--IFP_24_RL04_NBUW_26288 start -->
			<!--<version>10.1.19</version>-->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>10.1.25</version>-->
			<!--IFP_25_RL02_NBUW_41259 start-->
			<!--<version>10.1.34</version>-->
			<!-- IFP_25_RL03_NBUW_43543 modified start -->
			<!--<version>10.1.35</version> -->
			<version>10.1.40</version>
			<!-- IFP_25_RL03_NBUW_43543 modified end -->
			<!--IFP_25_RL02_NBUW_41259 end-->
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<!--IFP_24_RL04_NBUW_26288 end -->
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!-- 20220531 IFP_22_RL04_NBUW_1500 added start -->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-web</artifactId>
			<!--IFP_24_RL02_NBUW_16881 start -->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>6.1.7</version>-->
			<version>6.3.5</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<!--IFP_24_RL02_NBUW_16881 end -->
			<!--IFP_24_RL01_NBUW_13278 start -->
				<exclusions>
					<!--IFP_25_RL02_NBUW_35534 modified start-->
					<!--<exclusion>
                        <groupId>org.springframework</groupId>
                        <artifactId>spring-web</artifactId>
                    </exclusion>-->
					<!--IFP_25_RL02_NBUW_35534 modified end-->
					<!--IFP_24_RL02_NBUW_16881 start -->
                    <exclusion>
                        <artifactId>spring-security-core</artifactId>
                        <groupId>org.springframework.security</groupId>
                    </exclusion>
                    <!--IFP_24_RL02_NBUW_16881 end -->
                </exclusions>
			<!--IFP_24_RL01_NBUW_13278 start -->
		</dependency>
		<!-- 20220531 IFP_22_RL04_NBUW_1500 added end -->

		<!--IFP_23_RL02_NBUW_7689 start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>org.apache.tomcat.embed</groupId>-->
		<!--	<artifactId>tomcat-embed-core</artifactId>-->
			<!--IFP_23_RL04_NBUW_10604 start -->
			<!--<version>9.0.72</version>-->
            <!--<version>9.0.75</version> IFP_23_RL04_NBUW_12196 modified -->
			<!--IFP_23_RL04_NBUW_10604 end -->
			<!--IFP_23_RL04_NBUW_12196 start -->
			<!--<version>9.0.81</version>-->
			<!--IFP_23_RL04_NBUW_12196 start -->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!--IFP_23_RL02_NBUW_7689 end -->
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-codec-http</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>4.1.108.Final</version>-->
			<!--IFP_25_RL02_NBUW_41259 start-->
			<!--<version>4.1.115.Final</version>-->
			<version>4.1.118.Final</version>
			<!--IFP_25_RL02_NBUW_41259 end-->
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!--IFP_25_RL02_NBUW_41259 start-->
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-handler</artifactId>
			<version>4.1.118.Final</version>
		</dependency>
		<dependency>
			<groupId>io.netty</groupId>
			<artifactId>netty-common</artifactId>
			<version>4.1.118.Final</version>
		</dependency>
		<!--IFP_25_RL02_NBUW_41259 end-->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-core</artifactId>
			<!--IFP_24_RL04_NBUW_26288 start -->
			<!--<version>6.1.7</version>-->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>6.1.8</version>-->
			<version>6.3.5</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<!--IFP_24_RL04_NBUW_26288 end -->
			<scope>compile</scope>
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!-- Development Frameworks -->
		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<!-- fix run junit case slow compilation-->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>1.18.24</version>--> <!-- 20220825 IFP_22_RL05_NBUW_2376 added -->
			<version>1.18.30</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!-- Development Frameworks -->

		<!-- Spring Development Framework -->
		<!--IFP_21_RL04_SB_NB_19968 start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<exclusions>
				<exclusion>
					<groupId>org.hibernate</groupId>
					<artifactId>hibernate-core</artifactId>
				</exclusion>
			</exclusions>-->
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--<dependency>-->
		<!--	<groupId>org.hibernate</groupId>-->
		<!--	<artifactId>hibernate-core</artifactId>-->
		<!--	<version>5.5.2.Final</version>-->
		<!--</dependency>-->
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
			<exclusions>
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk15on</artifactId>
				</exclusion>
				<!--IFP_24_RL01_NBUW_13278 start -->
				<exclusion>
					<groupId>org.bouncycastle</groupId>
					<artifactId>bcprov-jdk18on</artifactId>
				</exclusion>
				<!--IFP_24_RL01_NBUW_13278 end -->
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcprov-jdk15on</artifactId>
			<!--IFP_23_RL02_NBUW_7689 start -->
			<version>1.69</version>
			<!--IFP_23_RL02_NBUW_7689 end -->
		</dependency>
		<!--IFP_21_RL04_SB_NB_19968 end -->
		<!--IFP_24_RL01_NBUW_13278 start -->
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<!--IFP_24_RL02_NBUW_16881 start -->
			<artifactId>bcprov-jdk18on</artifactId>
			<!--<version>1.74</version>-->
			<version>1.78</version>
			<!--IFP_24_RL02_NBUW_16881 end -->
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>spring-security-config</artifactId>
					<groupId>org.springframework.security</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--	<dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-web</artifactId>
                &lt;!&ndash;IFP_24_RL02_NBUW_16881 start &ndash;&gt;
                &lt;!&ndash;			<version>6.0.14</version>&ndash;&gt;
                &lt;!&ndash;IFP_24_RL04_NBUW_26288 start &ndash;&gt;
                &lt;!&ndash;<version>6.0.19</version> &ndash;&gt;
                <version>6.0.23</version>
                &lt;!&ndash;IFP_24_RL04_NBUW_26288 end &ndash;&gt;
                &lt;!&ndash;IFP_24_RL02_NBUW_16881 end &ndash;&gt;
            </dependency>-->
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<!--IFP_24_RL01_NBUW_13278 end -->
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--	<exclusions>
                    <exclusion>
                        <artifactId>tomcat-embed-websocket</artifactId>
                        <groupId>org.apache.tomcat.embed</groupId>
                    </exclusion>
                </exclusions>-->
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--	<dependency>
                <groupId>org.apache.tomcat.embed</groupId>
                <artifactId>tomcat-embed-websocket</artifactId>
                <version>10.1.19</version>
                <scope>compile</scope>
                <exclusions>
                    <exclusion>
                        <artifactId>tomcat-annotations-api</artifactId>
                        <groupId>org.apache.tomcat</groupId>
                    </exclusion>
                </exclusions>
            </dependency>-->
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!-- mssql -->
		<dependency>
			<groupId>com.microsoft.sqlserver</groupId>
			<artifactId>mssql-jdbc</artifactId>
		</dependency>

		<!-- Json lib -->

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<!--IFP_24_RL02_NBUW_16881 start -->
			<version>2.9.0</version>
			<!--IFP_24_RL02_NBUW_16881 end -->
			<!--IFP_23_RL02_NBUW_7689 start -->
			<exclusions>
				<exclusion>
					<groupId>net.minidev</groupId>
					<artifactId>json-smart</artifactId>
				</exclusion>
			</exclusions>
			<!--IFP_23_RL02_NBUW_7689 end -->
		</dependency>

		<!--IFP_23_RL02_NBUW_7689 start -->
		<dependency>
			<groupId>net.minidev</groupId>
			<artifactId>json-smart</artifactId>
			<version>2.4.9</version>
		</dependency>
		<!--IFP_23_RL02_NBUW_7689 end -->

		<dependency>
			<groupId>org.json</groupId>
			<artifactId>json</artifactId>
			<version>${org.json.version}</version>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
		</dependency>
		<!-- Json lib -->

		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!-- Mockito/JUnit lib -->
		<!--<dependency>-->
		<!--	<groupId>org.powermock</groupId>-->
		<!--	<artifactId>powermock-module-junit4</artifactId>-->
		<!--	<version>2.0.9</version>-->
		<!--	<scope>test</scope>-->
		<!--</dependency>-->
		<!--<dependency>-->
		<!--	<groupId>org.powermock</groupId>-->
		<!--	<artifactId>powermock-api-mockito2</artifactId>-->
		<!--	<version>2.0.9</version>-->
		<!--	<scope>test</scope>-->
		<!--</dependency>-->
		<!--<dependency>-->
		<!--	<groupId>org.powermock</groupId>-->
		<!--	<artifactId>powermock-api-easymock</artifactId>-->
		<!--	<version>2.0.9</version>-->
		<!--	<scope>test</scope>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!-- IFP_22_RL05_NBUW_2503 added start -->
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<!--IFP_24_RL01_NBUW_11658 start-->
			<!--<version>3.9.0</version>-->
			<!--<version>5.3.1</version>-->
			<version>5.11.0</version>
			<!--IFP_24_RL01_NBUW_11658 end-->
			<scope>test</scope>
		</dependency>
		<!-- IFP_22_RL05_NBUW_2503 added end -->

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--IFP_24_RL01_NBUW_11658 start-->
		<!--<dependency>-->
		<!--	<groupId>pl.pojo</groupId>-->
		<!--	<artifactId>pojo-tester</artifactId>-->
		<!--	<version>${pojo-tester.version}</version>-->
		<!--</dependency>-->
		<!--<dependency>-->
		<!--	<groupId>org.jmockit</groupId>-->
		<!--	<artifactId>jmockit</artifactId>-->
		<!--	<version>1.8</version>-->
		<!--	<scope>test</scope>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 end-->

		<!-- flywaydb - DB Script Migration -->

		<!-- 20240623 IFP_24_RL03_NBUW_23992 modified start -->
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-core</artifactId>
			<version>${flyway.version}</version>
		</dependency>
		<dependency>
			<groupId>org.flywaydb</groupId>
			<artifactId>flyway-sqlserver</artifactId>
		</dependency>
		<!-- 20240623 IFP_24_RL03_NBUW_23992 modified end -->

		<!-- flywaydb - DB Script Migration -->

		<dependency>
			<groupId>org.modelmapper</groupId>
			<artifactId>modelmapper</artifactId>
			<version>2.3.8</version>
		</dependency>

		<dependency>
			<groupId>org.jeasy</groupId>
			<artifactId>easy-rules-core</artifactId>
			<version>3.4.0</version>
		</dependency>

		<dependency>
			<groupId>org.mariuszgromada.math</groupId>
			<artifactId>MathParser.org-mXparser</artifactId>
			<version>4.4.2</version>
		</dependency>

		<dependency>
			<groupId>com.auth0</groupId>
			<artifactId>java-jwt</artifactId>
			<version>3.10.3</version>
			<!--IFP_23_RL02_NBUW_7689 start -->
			<exclusions>
				<exclusion>
					<groupId>com.fasterxml.jackson.core</groupId>
					<artifactId>jackson-databind</artifactId>
				</exclusion>
			</exclusions>
			<!--IFP_23_RL02_NBUW_7689 end -->
		</dependency>

		<!--IFP_23_RL02_NBUW_7689 start -->
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<!--IFP_24_RL02_NBUW_16881 start-->
			<!--<version>2.13.4</version>-->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>2.13.4.1</version>-->
			<version>2.17.2</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<!--IFP_24_RL02_NBUW_16881 end-->
		</dependency>
		<!--IFP_23_RL02_NBUW_7689 end -->

		<!--IFP_21_RL04_SB_NB_19968 start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>org.springframework.cloud</groupId>-->
		<!--	<artifactId>spring-cloud-starter-sleuth</artifactId>-->
		<!--</dependency>-->

		<!-- Circuit Breaker -->
		<!--<dependency>-->
			<!--<groupId>org.springframework.cloud</groupId>-->
			<!--<artifactId>spring-cloud-starter-netflix-hystrix</artifactId>-->
			<!--<version>2.2.6.RELEASE</version>--> <!-- IFP_22_RL05_NBUW_328 added -->
			<!--IFP_23_RL02_NBUW_7689 start -->
			<!--<exclusions>-->
			<!--	<exclusion>-->
			<!--		<groupId>com.google.guava</groupId>-->
			<!--		<artifactId>guava</artifactId>-->
			<!--	</exclusion>-->
			<!--</exclusions>-->
			<!--IFP_23_RL02_NBUW_7689 end -->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!--IFP_23_RL02_NBUW_7689 start -->
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<!--IFP_23_RL04_NBUW_10604 start -->
			<!--<version>24.1.1-jre</version>-->
			<version>32.0.0-jre</version>
			<!--IFP_23_RL04_NBUW_10604 end -->
		</dependency>
		<!--IFP_23_RL02_NBUW_7689 end -->

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-netflix-hystrix-dashboard</artifactId>
			<version>2.2.10.RELEASE</version>
			<exclusions>
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
			</exclusions> <!-- IFP_22_RL05_NBUW_328 added -->
		</dependency>

		<!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->

		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.4</version>
		</dependency>
		<!--IFP_21_RL04_SB_NB_19968 end -->
		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-spring</artifactId>
			<version>4.15.1</version>
		</dependency>

		<dependency>
			<groupId>net.javacrumbs.shedlock</groupId>
			<artifactId>shedlock-provider-jdbc-template</artifactId>
			<version>4.15.1</version>
		</dependency>

		<!--IFP_23_RL02_NBUW_7689 start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>org.springframework.cloud</groupId>-->
		<!--	<artifactId>spring-cloud-function-context</artifactId>-->
		<!--	<version>3.2.6</version>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!--IFP_23_RL02_NBUW_7689 end -->

		<!--IFP_24_RL04_NBUW_26288 end -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-function-context</artifactId>
			<version>4.1.2</version>
		</dependency>
		<!--IFP_24_RL04_NBUW_26288 end -->

		<!--IFP_21_RL04_SB_NB_19968 start -->
		<dependency>
			<groupId>org.jacoco</groupId>
			<artifactId>org.jacoco.agent</artifactId>
			<version>${jacoco.version}</version>
			<classifier>runtime</classifier>
		</dependency>
		<!--IFP_21_RL04_SB_NB_19968 end -->

		<!-- IFP_21_RL05_SB_NB_22171 start -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<!--IFP_24_RL01_NBUW_13278 start -->
			<!--<version>5.7.22</version>-->
			<!--<version>5.8.23</version>-->
			<version>5.8.25</version>
			<!--IFP_24_RL01_NBUW_13278 end -->
		</dependency>
		<!-- IFP_21_RL05_SB_NB_22171 end -->

		<!-- IFP_22_RL05_NBUW_328 added start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
			<!--IFP_24_RL02_NBUW_16881 start -->
			<exclusions>
				<exclusion>
					<artifactId>nimbus-jose-jwt</artifactId>
					<groupId>com.nimbusds</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-security-config</artifactId>
					<groupId>org.springframework.security</groupId>
				</exclusion>
			</exclusions>
			<!--IFP_24_RL02_NBUW_16881 end -->
			<!--IFP_24_RL01_NBUW_11658 commented start-->
			<!--<version>2.5.4</version>-->
			<!--IFP_24_RL01_NBUW_11658 commented end-->
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>com.nimbusds</groupId>
			<artifactId>nimbus-jose-jwt</artifactId>
			<version>9.37.2</version>
			<scope>compile</scope>
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 end -->
		<dependency>
			<groupId>com.azure.spring</groupId>
			<artifactId>azure-spring-boot-starter-active-directory</artifactId>
			<!-- IFP_25_RL03_NBUW_43543 modified start -->
			<!-- <version>3.12.0</version> --> 		<!-- IFP_22_RL05_NBUW_2503 added  -->
			<version>4.0.0</version>
			<!-- IFP_25_RL03_NBUW_43543 modified end -->
			<!--IFP_24_RL01_NBUW_11658 added start-->
			<exclusions>
				<exclusion>
					<artifactId>msal4j</artifactId>
					<groupId>com.microsoft.azure</groupId>
				</exclusion>
				<exclusion>
					<artifactId>validation-api</artifactId>
					<groupId>javax.validation</groupId>
				</exclusion>
				<!--IFP_24_RL02_NBUW_16881 start-->
				<exclusion>
					<artifactId>reactor-netty-core</artifactId>
					<groupId>io.projectreactor.netty</groupId>
				</exclusion>
				<exclusion>
					<artifactId>spring-security-config</artifactId>
					<groupId>org.springframework.security</groupId>
				</exclusion>
				<!--IFP_24_RL02_NBUW_16881 end-->
			</exclusions>
			<!--IFP_24_RL01_NBUW_11658 added end-->
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 start -->
		<!-- IFP_25_RL03_NBUW_43543 modified start -->
		<!--<dependency>-->
			<!--<artifactId>reactor-netty-core</artifactId>-->
			<!--<groupId>io.projectreactor.netty</groupId>-->
			<!--<version>1.0.39</version>-->
		<!--</dependency>-->
		<!-- IFP_25_RL03_NBUW_43543 modified end -->
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!-- 20230803 IFP_23_RL05_NBUW_7642 add start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>com.azure</groupId>-->
		<!--	<artifactId>azure-identity</artifactId>-->
		<!--	<version>1.8.1</version>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<dependency>
			<groupId>com.azure.spring</groupId>
			<artifactId>spring-cloud-azure-stream-binder-servicebus</artifactId>
			<!--IFP_24_RL01_NBUW_11658 start-->
			<!--<version>4.8.0</version>-->
			<!--IFP_24_RL05_CRR_1240 start-->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>5.13.0</version>-->
			<!--<version>5.3.0</version>-->
			<version>5.18.0</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<!--IFP_24_RL05_CRR_1240 start-->
			<!--IFP_24_RL01_NBUW_11658 start-->
			<!--IFP_24_RL03_NBUW_24876 start-->
			<exclusions>
				<exclusion>
					<groupId>org.jetbrains.kotlin</groupId>
					<artifactId>kotlin-stdlib</artifactId>
				</exclusion>
				<!--IFP_24_RL04_NBUW_26288 start -->
				<exclusion>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-function-context</artifactId>
				</exclusion>
				<!--IFP_24_RL04_NBUW_26288 end -->
			</exclusions>
			<!--IFP_24_RL03_NBUW_24876 end-->
		</dependency>
		<!-- 20230803 IFP_23_RL05_NBUW_7642 add end -->
		<!-- IFP_22_RL05_NBUW_2503 added start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>com.azure</groupId>-->
		<!--	<artifactId>azure-core</artifactId>-->
			<!-- 20230803 IFP_23_RL05_NBUW_7642 modify start -->
<!--		<version>1.31.0</version>-->
			<!--<version>1.39.0</version>-->
			<!-- 20230803 IFP_23_RL05_NBUW_7642 modify end -->
			<!--IFP_23_RL02_NBUW_7689 start -->
			<!--<exclusions>-->
			<!--	<exclusion>-->
			<!--		<groupId>com.fasterxml.woodstox</groupId>-->
			<!--		<artifactId>woodstox-core</artifactId>-->
			<!--	</exclusion>-->
			<!--</exclusions>-->
			<!--IFP_23_RL02_NBUW_7689 end -->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!-- IFP_22_RL05_NBUW_2503 added end -->

		<!--IFP_23_RL02_NBUW_7689 start -->
		<dependency>
			<groupId>com.fasterxml.woodstox</groupId>
			<artifactId>woodstox-core</artifactId>
			<version>6.4.0</version>
		</dependency>
		<!--IFP_23_RL02_NBUW_7689 end -->

		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>org.springframework.cloud</groupId>-->
		<!--	<artifactId>spring-cloud-openfeign-core</artifactId>-->
		<!--	<version>3.0.5</version>-->
			<!--IFP_23_RL02_NBUW_7689 start -->
			<!--<exclusions>-->
			<!--	<exclusion>-->
			<!--		<groupId>commons-fileupload</groupId>-->
			<!--		<artifactId>commons-fileupload</artifactId>-->
			<!--	</exclusion>-->
			<!--</exclusions>-->
			<!--IFP_23_RL02_NBUW_7689 end -->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!--IFP_23_RL02_NBUW_7689 start -->
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.5</version>
		</dependency>
		<!--IFP_23_RL02_NBUW_7689 end -->

		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>3.19.6</version>-->
			<version>3.25.5</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<dependency>
			<groupId>com.manulife.ap</groupId>
			<artifactId>rsf-config-client-azure</artifactId>
			<!-- 20230803 IFP_23_RL05_NBUW_7642 add start -->
			<exclusions>
				<!--IFP_24_RL01_NBUW_11658 start-->
				<!--<exclusion>-->
				<!--	<groupId>com.azure</groupId>-->
				<!--	<artifactId>azure-core</artifactId>-->
				<!--</exclusion>-->
				<exclusion>
					<artifactId>guava</artifactId>
					<groupId>com.google.guava</groupId>
				</exclusion>
				<!--IFP_24_RL01_NBUW_11658 end-->
			</exclusions>
			<!-- 20230803 IFP_23_RL05_NBUW_7642 add end -->
		</dependency>
		<!-- ContentCachingFilter -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>javax.ws.rs</groupId>-->
		<!--	<artifactId>jsr311-api</artifactId>-->
		<!--	<version>1.1.1</version>-->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!-- CASRetryPolicyHandlerTest -->
		<dependency>
			<groupId>com.sun.jersey</groupId>
			<artifactId>jersey-core</artifactId>
			<version>1.19.4</version>
		</dependency>
		<!-- IFP_22_RL05_NBUW_328 added end -->

		<!--20220519 IFP_22_RL04_NBUW_739 added start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
			<!--<version>5.5.7</version>-->
			<!--IFP_25_RL02_NBUW_41259 start-->
			<!--<version>6.3.5</version>-->
			<version>6.3.8</version>
			<!--IFP_25_RL02_NBUW_41259 end-->
		</dependency>
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!--20220519 IFP_22_RL04_NBUW_739 added end -->

		<dependency>
			<groupId>nl.jqno.equalsverifier</groupId>
			<artifactId>equalsverifier</artifactId>
			<version>3.10.1</version>
		</dependency>
		<!--20220906 IFP_22_RL05_NBUW_2175 added start -->
		<!--IFP_24_RL01_NBUW_11658 commented start-->
		<!--<dependency>-->
		<!--	<groupId>org.yaml</groupId>-->
		<!--	<artifactId>snakeyaml</artifactId>-->
			<!--IFP_23_RL02_NBUW_7689 start -->
			<!--<version>1.31</version>-->
			<!--<version>1.32</version>-->
			<!--IFP_23_RL02_NBUW_7689 end -->
		<!--</dependency>-->
		<!--IFP_24_RL01_NBUW_11658 commented end-->
		<!--20220906 IFP_22_RL05_NBUW_2175 added end -->
		<!-- 20221018 IFP_22_RL05_NBUW_3866 added start -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<!--IFP_23_RL02_NBUW_7689 start -->
			<!--<version>1.14.2</version>-->
			<version>1.15.3</version>
			<!--IFP_23_RL02_NBUW_7689 end -->
		</dependency>
		<!-- 20221018 IFP_22_RL05_NBUW_3866 added end -->
		<!--IFP_23_RL02_NBUW_5677 start-->
		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>2.10.0</version>
		</dependency>

		<!-- apache commons-text-->
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-text</artifactId>
			<version>1.10.0</version>
		</dependency>
		<!--IFP_23_RL02_NBUW_5677 end-->
		<!-- IFP_23_RL02_NBUW_6958 start -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>2.0.23</version>
		</dependency>
		<!-- IFP_23_RL02_NBUW_6958 end -->
		<!-- 20231124 IFP_24_RL01_NBUW_11438 added start -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>transmittable-thread-local</artifactId>
			<version>2.14.3</version>
		</dependency>
		<!-- 20231124 IFP_24_RL01_NBUW_11438 added end -->
		<!--IFP_24_RL02_NBUW_16881 start -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>1.4.14</version>-->
			<version>1.5.13</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
			<exclusions>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-core</artifactId>
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<version>1.4.14</version>-->
			<version>1.5.13</version>
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 end -->
		<!--IFP_24_RL01_NBUW_11658 added start-->
		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing</artifactId>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-tracing-bridge-brave</artifactId>
		</dependency>
		<!--IFP_24_RL02_NBUW_16881 start-->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
			<version>3.3.7</version>  <!-- IFP_25_RL03_NBUW_43543 added -->
			<!--IFP_25_RL02_NBUW_35534 modified start-->
			<!--<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-actuator-autoconfigure</artifactId>
				</exclusion>
			</exclusions>-->
			<!--IFP_25_RL02_NBUW_35534 modified end-->
		</dependency>
		<!-- IFP_25_RL03_NBUW_43543 added start -->
        <dependency>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
            <version>1.3.2</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
        </dependency>
		<!-- IFP_25_RL03_NBUW_43543 added end -->
        <!--IFP_24_RL02_NBUW_16881 end-->
		<!--IFP_25_RL02_NBUW_35534 modified start-->
		<!--<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
			&lt;!&ndash;IFP_24_RL02_NBUW_16881 start&ndash;&gt;
			<version>3.1.6</version>
			<exclusions>
				<exclusion>
					<artifactId>spring-boot-actuator</artifactId>
					<groupId>org.springframework.boot</groupId>
				</exclusion>
			</exclusions>
			&lt;!&ndash;IFP_24_RL02_NBUW_16881 end&ndash;&gt;
		</dependency>
		&lt;!&ndash;IFP_24_RL02_NBUW_16881 start&ndash;&gt;
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator</artifactId>
			<version>3.1.6</version>
			<scope>compile</scope>
		</dependency>-->
		<!--IFP_25_RL02_NBUW_35534 modified end-->
		<!--IFP_24_RL02_NBUW_16881 end-->
		<!--IFP_24_RL01_NBUW_11658 added end-->
	</dependencies>

	<!-- IFP_22_RL05_NBUW_328 added start-->
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.manulife.ap</groupId>
				<artifactId>rsf-config-client-dependencies</artifactId>
				<!--IFP_24_RL01_NBUW_11658 start-->
				<!--<version>1.3.0</version>-->
				<!--IFP_25_RL02_NBUW_35534 modified start-->
				<!--<version>2.0.0</version>-->
				<version>${rsf.version}</version>
				<!--IFP_25_RL02_NBUW_35534 modified end-->
				<!--IFP_24_RL01_NBUW_11658 end-->
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!--IFP_24_RL01_NBUW_11658 added start-->
			<dependency>
				<groupId>io.micrometer</groupId>
				<artifactId>micrometer-tracing-bom</artifactId>
				<version>${micrometer-tracing.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<!--IFP_24_RL01_NBUW_11658 added end-->
		</dependencies>
	</dependencyManagement>
	<!-- IFP_22_RL05_NBUW_328 added end-->

	<build>
		<resources>
			<resource>
				<directory>src/main/resources</directory>
				<excludes>
					<exclude>local/*</exclude>
					<exclude>pcf/*</exclude>
				</excludes>
			</resource>
			<resource>
				<directory>src/main/resources/${profiles.active}</directory>
			</resource>
		</resources>
		<plugins>
			<!--IFP_21_RL04_SB_NB_19968 start -->
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<!-- IFP_22_RL05_NBUW_328 added start -->
				<!--IFP_24_RL01_NBUW_11658 start-->
				<!--version>2.3.6.RELEASE</version>-->
				<!--IFP_25_RL02_NBUW_35534 modified start-->
				<!--<version>3.1.2</version>-->
				<version>3.3.5</version>
				<!--IFP_25_RL02_NBUW_35534 modified end-->
				<!--IFP_24_RL01_NBUW_11658 end-->
				<executions>
					<execution>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
				<configuration>
					<addResources>true</addResources>
					<mainClass>${start-class}</mainClass>
				</configuration>
				<!-- IFP_22_RL05_NBUW_328 added end -->
			</plugin>

			<!-- IFP_22_RL05_NBUW_328 added start -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId> <!-- 20220830 IFP_22_RL05_NBUW_2175 added -->
				<artifactId>maven-clean-plugin</artifactId>
				<version>${maven-clean-plugin.version}</version>
				<executions>
					<execution>
						<id>auto-clean</id>
						<phase>install</phase>
						<goals>
							<goal>clean</goal>
						</goals>
						<configuration>
							<excludeDefaultDirectories>true</excludeDefaultDirectories>
							<followSymLinks>false</followSymLinks>
							<filesets>
								<fileset>
									<directory>${basedir}</directory>
									<includes>
										<include>Dockerfile</include>
									</includes>
								</fileset>
							</filesets>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!-- IFP_22_RL05_NBUW_328 added end -->
			<!-- 20220825 IFP_22_RL05_NBUW_2376 added start -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<!--IFP_24_RL01_NBUW_11658 start-->
				<!--IFP_25_RL02_NBUW_35534 modified start-->
				<!--<version>3.10.1</version>-->
				<version>3.13.0</version>
				<!--IFP_25_RL02_NBUW_35534 modified end-->
				<!--IFP_24_RL01_NBUW_11658 end-->
				<configuration>
					<source>${maven.compiler.source}</source>
					<target>${maven.compiler.target}</target>
				</configuration>
			</plugin>
			<!-- 20220825 IFP_22_RL05_NBUW_2376 added end -->
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<!--IFP_24_RL01_NBUW_11658 start-->
				<!--<version>2.19.1</version>-->
				<!--IFP_25_RL02_NBUW_35534 modified start-->
				<!--<version>3.0.0</version>-->
				<version>3.2.5</version>
				<!--IFP_25_RL02_NBUW_35534 modified end-->
				<!--IFP_24_RL01_NBUW_11658 end-->
				<configuration>
					<skipTests>${skip.unit.tests}</skipTests>
					<systemPropertyVariables>
						<jacoco-agent.destfile>${project.build.directory}/coverage.exec</jacoco-agent.destfile>
					</systemPropertyVariables>
				</configuration>
			</plugin>
			<!--IFP_21_RL04_SB_NB_19968 end -->

			<!-- for release -->
			<plugin>
				<groupId>external.atlassian.jgitflow</groupId>
				<artifactId>jgitflow-maven-plugin</artifactId>
				<version>1.0-m5.1</version>
				<configuration>
					<flowInitContext>
						<masterBranchName>${masterBranchName}</masterBranchName>
						<developBranchName>${developBranchName}</developBranchName>
						<versionTagPrefix>version-</versionTagPrefix>
						<featureBranchPrefix>feature-</featureBranchPrefix>
						<releaseBranchPrefix>release-</releaseBranchPrefix>
						<hotfixBranchPrefix>hotfix-</hotfixBranchPrefix>
					</flowInitContext>
					<noDeploy>true</noDeploy>
					<allowUntracked>true</allowUntracked>
					<allowSnapshots>true</allowSnapshots>
					<scmCommentPrefix>${scmCommentPrefix}</scmCommentPrefix>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>xerces</groupId>
						<artifactId>xercesImpl</artifactId>
						<version>2.11.0</version>
					</dependency>
					<dependency>
						<groupId>com.jcraft</groupId>
						<artifactId>jsch</artifactId>
						<version>0.1.54</version>
					</dependency>
				</dependencies>
			</plugin>
			<!--IFP_21_RL04_SB_NB_19968 start -->
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
				<version>${jacoco.version}</version>
				<executions>
					<execution>
						<id>default-prepare-agent</id>
						<goals>
							<goal>prepare-agent</goal>
						</goals>
					</execution>
					<execution>
						<id>default-report</id>
						<goals>
							<goal>report</goal>
						</goals>
					</execution>
					<execution>
						<id>default-check</id>
						<goals>
							<goal>check</goal>
						</goals>
						<configuration>
							<rules>
								<rule>
									<element>BUNDLE</element>
									<limits>
										<limit>
											<counter>COMPLEXITY</counter>
											<value>COVEREDRATIO</value>
											<minimum>0.0</minimum>
										</limit>
									</limits>
								</rule>
							</rules>
							<excludes>
								<exclude>
									**/config/**,
									**/util/**,
									**/lov/**,
									<!-- IFP_23_RL02_NBUW_6947 added start -->
									**/pe/**,
									**/properties/**
									<!-- IFP_23_RL02_NBUW_6947 added end -->
								</exclude>
							</excludes>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<!--IFP_21_RL04_SB_NB_19968 end -->
		</plugins>
	</build>
	<!-- Profiles -->
	<profiles>
		<profile>
			<id>pcf</id>
			<properties>
				<activatedProperties>pcf</activatedProperties>
				<sonar.host.url>https://sonar.ap.manulife.com/ver83</sonar.host.url>
				<sonar.links.homepage>${project.url}</sonar.links.homepage>
				<sonar.language>java</sonar.language>
				<sonar.links.scm>${project.scm.url}</sonar.links.scm>
			</properties>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
		</profile>

	</profiles>
</project>
