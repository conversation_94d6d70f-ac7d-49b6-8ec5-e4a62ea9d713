#IFP_24_RL05_NBUW_25641 added Start
common:
  token_url: https://login.microsoftonline.com/5d3e2773-e07f-4432-a630-1a0f68a28a05/oauth2/v2.0/token
  client_id: d4aacef8-bf73-49c0-a647-905a710fe3b7
  secret: ****************************************
#IFP_24_RL05_NBUW_25641 added End
spring:
  application:
    name: hk-ifp-nb-core-service
  jpa:
    show-sql: true
    hibernate:
      ddl-auto: none
    database-platform: org.hibernate.dialect.SQLServer2012Dialect
  cache:
    type: none
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    # SIT 2
#    query:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      hikari:
#        connection-test-query: SELECT 1
#      platform: mssql
#      validation-query: select 1
#      jdbc-url: *********************************************************************************************************
#      username: hkit
#      password: 'ZQTP3H3yMjGAsNnB'
    # local
#    query:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      hikari:
#        connection-test-query: SELECT 1
#      platform: mssql
#      validation-query: select 1
#      jdbc-url: ******************************************************************************************************
#      username: sa
#      password: 'Pass@Word1'
    # dev 4
    query:
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      hikari:
        connection-test-query: SELECT 1
      platform: mssql
      validation-query: select 1
      jdbc-url: ***************************************************************;databaseName=hk-ifp-nb-core-query-db-dev4
      username: hkit
      password: 'ViNvX%\p3ThTf=/nFbnd'
    # UAT 2
#    query:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      hikari:
#        connection-test-query: SELECT 1
#      platform: mssql
#      validation-query: select 1
#      jdbc-url: *********************************************************************************************************
#      username: HKNBUW01
#      password: 'u-j\C32WBo4V+uo'
    # SIT 2
#    command:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      hikari:
#        connection-test-query: SELECT 1
#      platform: mssql
#      validation-query: select 1
#      jdbc-url: ***********************************************************************************************************
#      username: hkit
#      password: 'ZQTP3H3yMjGAsNnB'
    # DEV 2
    command:
      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
      hikari:
        connection-test-query: SELECT 1
      platform: mssql
      validation-query: select 1
      jdbc-url: ***************************************************************;databaseName=hk-ifp-nb-core-command-db-dev4
      username: hkit
      password: 'ViNvX%\p3ThTf=/nFbnd'
    # UAT 2
#    command:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      hikari:
#        connection-test-query: SELECT 1
#      platform: mssql
#      validation-query: select 1
#      jdbc-url: ***********************************************************************************************************
#      username: HKNBUW01
#      password: 'u-j\C32WBo4V+uo'
    # local
#    command:
#      driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
#      hikari:
#        connection-test-query: SELECT 1
#      platform: mssql
#      validation-query: select 1
#      jdbc-url: ********************************************************************************************************
#      username: sa
#      password: 'Pass@Word1'

  flyway:
    baseline-on-migrate: true
    baseline-version: 0
    clean-disabled: false
    out-of-order: true
    clean-on-validation-error: true
    enabled: true
    url: ***************************************************************
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    user: hkit
    password: 'ViNvX%\p3ThTf=/nFbnd'
    locations:
      - classpath:cicd/flyway/db/query
    schemas:
      - dbo

  lifecycle:
    timeout-per-shutdown-phase: 6s #graceful shutdown period
  cloud:
    #IFP_24_RL01_NBUW_11658 added start
    azure:
      active-directory:
        enabled: true
        application-type: resource_server
        credential:
          client-id: ${common.client_id}
    openfeign:
      client:
        config:
          default:
            loggerLevel: BASIC
            connectTimeout: 120000
            readTimeout: 120000
    function:
      definition: consumeFanoutExchange;consumeExchange;consumeCustomermerge
    #IFP_24_RL01_NBUW_11658 added end
    stream:
      output-bindings: supplyDataSync-out-0;supplyFanoutExchange-out-0;supplyExchange-out-0;supplyNblinkage-out-0
      binders:
        servicebus-nbuws:
          type: servicebus
          environment:
            spring:
              cloud:
                azure:
                  servicebus:
                    credential:
                      client-id: ${common.client_id}
                      client-secret: ${common.secret}
                    profile:
                      tenant-id: 5d3e2773-e07f-4432-a630-1a0f68a28a05
                    namespace: mfcsbussharesiteas01
        servicebus-cust:
          type: servicebus
          environment:
            spring:
              cloud:
                azure:
                  servicebus:
                    credential:
                      client-id: ${common.client_id}
                      client-secret: ${common.secret}
                    profile:
                      tenant-id: 5d3e2773-e07f-4432-a630-1a0f68a28a05
                    namespace: mfcsbuscustsiteas01
      bindings:
        supplyDataSync-out-0:
          binder: servicebus-nbuws
          destination: hk-nbuw-nbuws-nbcore-datasync-02
          content-type: application/json
        supplyFanoutExchange-out-0:
          binder: servicebus-nbuws
          destination: hk-nbuw-nbuws-nbcore-fanoutexchange-02
          content-type: application/json
        consumeFanoutExchange-in-0:
          binder: servicebus-nbuws
          destination: hk-nbuw-nbuws-nbcore-fanoutexchange-02
          group: hk-nbuw-nbuws-nbcore-fanoutexchange-sub-02
          content-type: application/json
        supplyExchange-out-0:
          binder: servicebus-nbuws
          destination: hk-nbuw-nbuws-nbcore-exchange-02
          content-type: application/json
        consumeExchange-in-0:
          binder: servicebus-nbuws
          destination: hk-nbuw-nbuws-nbcore-exchange-02
          group: hk-nbuw-nbuws-nbcore-exchange-sub-02
          content-type: application/json
        consumeCustomermerge-in-0:
          binder: servicebus-cust
          destination: hk-cp-csms-customermerge-02
          group: hk-nbuw-nbuws-nbcore-customermerge-02
          content-type: application/json
        supplyNblinkage-out-0:
          destination: hk-nb-linkage-02
          content-type: text/plain
          binder: servicebus-cust
      servicebus:
        bindings:
          supplyDataSync-out-0:
            producer:
              entity-type: topic
              sync: true
              send-timeout: 3000
              retry:
                mode: fixed
                fixed:
                  delay: 3000
                  max-retries: 3
          supplyFanoutExchange-out-0:
            producer:
              entity-type: topic
          consumeFanoutExchange-in-0:
            consumer:
              auto-complete: false
          supplyExchange-out-0:
            producer:
              entity-type: topic
          consumeExchange-in-0:
            consumer:
              auto-complete: false
          supplyNblinkage-out-0:
            producer:
              entity-type: topic
              sync: true
              send-timeout: 9000
              maxConcurrentSessions: 50
              retry:
                mode: fixed
                fixed:
                  delay: 3000
                  max-retries: 3
          consumeCustomermerge-in-0:
            consumer:
              maxConcurrentSessions: 50
              auto-complete: false
        poller:
          fixed-delay: 1000
          initial-delay: 0
  sleuth:
    log:
      slf4j:
        whitelisted-mdc-key : policynokey
    propagation-keys : policynokey
logging:
  level:
    org.springframework: debug
    com.azure.core.amqp.implementation: debug
    com.azure.messaging.servicebus: debug
  config: src/main/resources/logback.xml
management:
  endpoints:
    web:
      exposure:
        include: heapdump,loggers,threaddump,info,metrics,health,refresh
    metrics:
      enabled: true
  info:
    git:
      mode: full
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  metrics:
    enabled: true
  export:
    prometheus:
      enabled: true
  endpoint:
    health:
      show-details: always
      probes:
        enabled: true
      livenessState:
        enabled: true
      readinessState:
        enabled: true
      group:
        liveness:
          include: livenessState
          show-details: always
        readiness:
          include: readinessState
          show-details: always
    env:
      keys-to-sanitize: .*password*., .*secret*., .*key*., .*token*, .*credentials*., .*vcap_services*., .*azure.*.connection-string.*, .*azure.*.keyvault.*, .*client_id*., .*client_key*., .*tenant_id*.,

eureka:
  client:
    register-with-eureka: false
    fetch-registry: false

rsf.core.gracefulShutdown.enabled: true

datasync:
  enabled: false
  
external:
  nb-acl:
#    url: https://soaut3app.ap.manulife.com:9444/IFPNBServiceWeb
#    url: https://soaut2app.ap.manulife.com:9444/IFPNBServiceWeb
    url: https://soadv2app.ap.manulife.com:9443/IFPNBServiceWeb
    #url: https://soaltapp1.ap.manulife.com:9443/IFPNBServiceWeb
    token: Ynlvcl9zb2FfdXNlcjpCeW9yQDEyMzQ=
    userName: byor_soa_user
    password: Byor@1234
  fna-service:
    #url: http://svc-hk-ifp-fna-service-2.ns-hkg-policy-system:8080/api/v2
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-ifp-fna-service-2/api/v2
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://3b4f4199-0b7a-4d51-8c90-bf35a42261b7/.default
  camp-service:
    #url: http://svc-hk-campaign-rules-adapter-service.ns-hkg-shared-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-campaign-rules-adapter-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://1f693a65-c235-4e15-a614-d9c40e891358/.default # IFP_22_RL05_NBUW_251 added
  billing-service:
    url: https://eas-hk-uat-api.ap.manulife.com/int/hk-billing-service-2/
    token:
      url: ${common.token_url}
      clientid: 9c7d098d-2d04-4a9b-892a-8fcac7dbacbb
      clientsecret: ****************************************
      granttype: client_credentials
      scope: api://1f693a65-c235-4e15-a614-d9c40e891358/.default
  crs-service:
    #url: http://svc-hk-ifp-crs-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-ifp-crs-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://71cbb830-1eac-465c-a843-ca5105b0dacd/.default # IFP_22_RL04_NBUW_1454 added
  customer-service:
    #url: http://svc-customer-management-service-2.ns-hkg-customer-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/customer-management-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://5c668c09-90f3-4685-b18e-dffedbf2df1c/.default
  hk-party-customer-service:
    #url: https://eas-hk-sit-api.ap.manulife.com/int/hk-party-customer-domain-service/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-party-customer-domain-service/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://3e3abd3d-3f70-4e15-93f1-575d9c10a500/.default # IFP_23_RL04_NBUW_1729 added
  awd-service:
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-awd-bpmportal-2/api
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://e8a3ecf4-404c-4201-9682-dcdc090e14b2/.default #IFP_22_RLO4_NBUW_1657 added
  cpdts-service:
    #url: http://svc-cpdts-bff-service-env2.ns-hkg-agent-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/cpdts-bff-service-env2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://f0250623-3bec-4156-a780-d66f9576ced2/.default
  benefit-premium-service:
    #url: http://svc-hk-benefit-premium-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-benefit-premium-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://3760cbed-9129-4234-92c9-a7d5ec22838e/.default
  bridgerScan-service:
    #url: http://svc-hk-bridger-scan-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-bridger-scan-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://2d7564ec-4a98-4c10-902c-2baf22d3f854/.default # IFP_22_RL04_NBUW_1456 added
  annual-campaign-service:
    #url: http://svc-hk-common-campaign-service.ns-hkg-shared-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-common-campaign-service/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://1f693a65-c235-4e15-a614-d9c40e891358/.default
  product-benefit-service:
    #url: http://svc-hk-product-benefit-service-2.ns-hkg-policy-system:8080
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-product-benefit-service-2
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://9b5dc3db-bc3a-42b0-ba2c-39ea8291c616/.default
  agent-data-service:
    #url: http://svc-agent-data-service-2.ns-hkg-agent-system:8080
    url: https://eas-hk-sit-api.ap.manulife.com/int/agent-data-service-2
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://6a5ccb1d-a3a9-4df5-8f0b-9886064d2953/.default
  hk-product-service:
    #url: http://svc-hk-product-service-2.ns-hkg-policy-system:8080
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-product-service-2
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      granttype: client_credentials
      scope: api://29c098c0-735d-4c40-b9bb-e1445a121ba2/.default
  product-engine-service:
    #url: http://svc-pos-pe-nodejs-hkg-st2.ns-hkg-pos-system:8080
    url: https://eas-hk-sit-api.ap.manulife.com/int/pos-pe-nodejs-hkg-st2
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://fcab398e-a54b-417d-acc3-123e67a9ae73/.default
  #20230316 IFP_23_RL02_NBUW_6947 added end
  #IFP_23_RL04_NBUW_7176 start
  payment-service:
    #url: http://svc-hk-payment-misc-service-2.ns-hkg-shared-system:8080/
    #url: https://eas-hk-sit-api.ap.manulife.com/int/hk-payment-misc-service-2/
    url: https://eas-hk-uat-api.ap.manulife.com/int/hk-payment-misc-service-2/
    token:
      url: ${common.token_url}
      clientid: 9c7d098d-2d04-4a9b-892a-8fcac7dbacbb
      clientsecret: ****************************************
      #IFP_24_RL05_NBUW_25641 modified End
      grantType: client_credentials
      scope: 'api://884a1d7d-c143-48bd-8e86-0bd6e77a3118/.default'
      expiryThreshold: 60
  #IFP_23_RL04_NBUW_7176 end
  #IFP_23_RL04_NBUW_7934 start
  fund-service:
    #url: http://svc-hk-fund-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-fund-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://e6d85cc9-96c0-41b6-b5af-e156e1ecb22d/.default
  #IFP_23_RL04_NBUW_7934 end
  #20230316 IFP_23_RL02_NBUW_6947 added end
  #IFP_23_RL03_NBUW_7367 added start
  hk-policy-service:
    #url: http://svc-hk-policy-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-policy-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://c17adab9-a685-4f44-bab0-7b9a290ac346/.default
  #IFP_23_RL03_NBUW_7367 added end
  #IFP_24_RL01_NBUW_10799 added start
  hk-pm-suitability-service:
    #url: http://svc-hk-pm-suitability-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-uat-api.ap.manulife.com/int/hk-pm-suitability-service-2/
    token:
      url: ${common.token_url}
      clientid: 9c7d098d-2d04-4a9b-892a-8fcac7dbacbb
      clientsecret: ****************************************
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://3f53dadc-e00c-494a-9daa-549d29990fe9/.default
  #IFP_24_RL01_NBUW_10799 added end
  #IFP_24_RL01_NBUW_12952 added start
  hk-phi-service:
    #url: http://svc-hk-ifp-phi-service-2.ns-hkg-policy-system:8080/
    url: https://eas-hk-sit-api.ap.manulife.com/int/hk-ifp-phi-service-2/
    token:
      url: ${common.token_url}
      clientid: ${common.client_id}
      clientsecret: ${common.secret}
      #IFP_24_RL05_NBUW_25641 modified End
      granttype: client_credentials
      scope: api://8f530b1f-3913-4204-b9de-2d86836dc7bd/.default
  #IFP_24_RL01_NBUW_12952 added end
  
estaGracefulShutdownWaitSeconds: 30

scheduler:
  pollCasStatus:
    casStatusCheckProc: CHECK_CAS_STATUS
    interval: PT20M
    lockName: POLL_CAS_STATUS_LOCK
    lockAtLeastFor: PT19M
    lockAtMostFor: PT19M
    delayRetryAfterCASUp: 1 # minutes
    delayRetryAfterAppStart: 5 #minutes
    scheduledRetryStart: "03:30"
    scheduledRetryEnd: "05:30"
  houseKeepTask:
    cleanUpCutOff: 180
  # 20221017 IFP_22_RL05_NBUW_3907 added start
  supportNewProductTask:
    enabled: true
    runTime: "0 */10 * * * ?"
    #runTime: "0 00 15 * * ?"
    #IFP_23_RL04_NBUW_4901 start
    lockName: SUPPORT_NEW_PRODUCT_LOCK
    lockAtLeastFor: PT5M
    lockAtMostFor: PT60M
    #IFP_23_RL04_NBUW_4901 end
  # 230113 IFP_23_RL02_NBUW_5677 start
  schedulerTaskConfig:
    corePoolSize: 20
    maxPoolSize: 50
    queueCapacity: 50
  # 230113 IFP_23_RL02_NBUW_5677 end
  #IFP_23_RL02_NBUW_2349 end
  fixFMSyncIssueForPendingPolicyTask:
    #runTime: "0 00 04 6 11 ?"
    runTime: "0 */1 * * * ?"
    enabled: false
  #IFP_23_RL02_NBUW_2349 end
  # 20230629 IFP_23_RL03_HIU_508 added start
  supportServiceBusTask:
    runCronTime: "0 */10 * * * ?"
    lockAtMostFor: "PT20S" #IFP_23_RL03_NBUW_7150 modified
    lockAtLeastFor: "PT5S" #IFP_23_RL03_NBUW_7150 modified
  # 20230629 IFP_23_RL03_HIU_508 added end
  # 20230818 IFP_23_RL05_NBUW_11713 added start
  serviceBusEmailTask:
    runCronTime: "0 */6 * * * ?"
    lockAtMostFor: "PT10S"
    lockAtLeastFor: "PT5S"
  customerMergeRetryTask:
    runCronTime: "0 0 * * * ? "
    lockAtMostFor: "PT10M"
    lockAtLeastFor: "PT10M"
  dailyCustomerMergeRetryTask:
    runCronTime: "0 0 4 * * ? "
    lockAtMostFor: "PT10M"
    lockAtLeastFor: "PT10M"
  # 20230818 IFP_23_RL05_NBUW_11713 added end
  # 20230818 IFP_23_RL04_NBUW_7367 added start
  nBMqLogTableCleanTask:
    lockAtMostFor: "PT10M"
    lockAtLeastFor: "PT10M"
  #IFP_24_RL02_NBUW_14087 start
    runTime: "0 00 04 * * ?"
  #IFP_24_RL02_NBUW_14087 end
  # 20230818 IFP_23_RL04_NBUW_7367 added end
  # 20231207 IFP_24_RL01_NBUW_11438 added start
  syncClientIdTask:
    # 20240228 IFP_24_RL02_NBUW_13978 modified start
    # enabled: true
    enabled: false
    # 20240228 IFP_24_RL02_NBUW_13978 modified end
    runCronTime: "0 0 10 8 12 ?"
    lockAtMostFor: "PT10M"
    lockAtLeastFor: "PT1M"
  # 20231207 IFP_24_RL01_NBUW_11438 added end
  #IFP_24_RL02_NBUW_14087 start
  scheduleLogTableCleanUp:
    runTime: "0 30 12 * * ?"
    # 20240603 IFP_24_RL03_NBUW_22715 added start
    lockAtMostFor: "PT60M"
    lockAtLeastFor: "PT10M"
    # 20240603 IFP_24_RL03_NBUW_22715 added end
  #IFP_24_RL02_NBUW_14087 end
appConfig:
  flyway:
    datasources:
      query:
        location: classpath:cicd/flyway/db/query
        service: hk-ifp-nb-core-service-query-db
  oauth2Template:
    expiryThreshold: 60 #sec
  feignRetry:
    noOfAttempts: 0
    retryInterval: 30000
  #IFP_24_RL04_NBUW_14183 start
  executor:
    corePoolSize: 4
    maxPoolSize: 100
    queueCapacity: 10
    callerRunsPolicy: true
  #IFP_24_RL04_NBUW_14183 end

sysConfig:
  enableIssueToCAS: Y
  enableSTPControl: Y

userInfo:
  verifyTokenEnabled: Y
  verifyTokenEnabledUserIds: RPA
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAo5oNFGqjG9/kQmlIh6btVWFISsJSmVPITOwyEDa4DmoabD8iA1qrwMLiLMarvmw/5/b6GcJW16ZNytAVz7LJWAiVh2w9EFB3eb/BxpPZ5eEkEhvsaUDiZNS1uJmM5dtsdN4Mpj1U0cw49gVT3AWIVlzDlESAqbe6XHpfB2MregDTXPaDneFi89eh49LR+4kBPZQd1nG9mNStLLSmeEAZGDCp4F0YjykyUHqJj+WEJocFNQUI+QQMYXZGmdMZMYLUv69Vorar095PEc8rJ7nzp0qWYS/0LlMHKKSk7bjeinkeJuRqmlEBeigjK1AHpDICByAQX8izirL8D1e69vn+uQIDAQAB
#------------------ Different Config ---------------------#

oauth2:
  resource:
    id: hk-ifp-nb-core-service

#IFP_22_RL05_NBUW_262 added start
azure:
  activedirectory:
    tenant-id: 5d3e2773-e07f-4432-a630-1a0f68a28a05
    #IFP_24_RL05_NBUW_25641 modified Start
    #client-id: d4aacef8-bf73-49c0-a647-905a710fe3b7
    client-id: ${common.client_id}
    #IFP_24_RL05_NBUW_25641 modified End

server:
  shutdown: graceful
  port: 8081
#IFP_22_RL05_NBUW_262 added end
#20230316 IFP_23_RL02_NBUW_6947 added start
product-engine:
  enabled: true
#20230316 IFP_23_RL02_NBUW_6947 added end
