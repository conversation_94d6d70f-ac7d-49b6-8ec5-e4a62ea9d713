//  Dockerfile
//
//  Copyright (c) 2022 Manulife International Ltd.
//
//  Description:
//
//  Maintenance History
//
//  YYMMDD Who              Reason
//  ====== ================ ==================================================================================================================================
//  220304 Everrett Xiang   IFP_22_RL05_NBUW_328 - NB-CORE AKS Migration
//  240902 Jason Zhang      IFP_24_RL04_NBUW_24514 - [RP ILAS] [PHI service] change pipelineRepoBranches from nbuws_dev_2 to nbuws_dev
//  250520 (MITDC CD) Auter Zhang  IFP_25_RL03_NBUW_43798 - [Tech]adjust nb core /nb bff for bitbucket migration in 2025 Jun
//pipelineJavaMavenAksDeployment([
pipelineJavaMavenAksDeploymentPlus([
    // IFP_25_RL03_NBUW_43798 modified start
    // pipelineRepository: "ssh://***********************:8080/hksoa-policy/hk-ifp-nb-core-service-cicd.git",
    pipelineRepository: "**************:mfc-hongkong/hk-ifp-nb-core-service-cicd.git",
    // IFP_25_RL03_NBUW_43798 modified end
    pipelinePropertiesFolder: "pipeline",
    commonPropertiesFileName: "pipeline/jenkins/common/pipeline.properties",
    pipelineRepoBranches: [[name:"feature/nbuws_dev"]],
    propertiesFileName: "pipeline/jenkins/env1/uat/dr-cd.properties",
    HELM_ROLLBACK: "false"
])
