------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_ILAS_RESULT](
	[POLICY_KEY] [NVARCHA<PERSON>](18) NOT NULL,
	[CONTRACT_NO] [NVARCHAR](10) NULL,
	[DURATION_SUITABLE] [NVARCHAR](1) NULL,
	[RISK_SUITABLE] [NVARCHAR](1) NULL,
	[RPQ_SCORE] [NUMERIC](22) NULL,
	[RPQ_SCORE_CALC] [NUMERIC](22) NULL,
	[RPQ_RISK_LVL] [NUMERIC](22) NULL,
	[INV_RISK_LVL] [NUMERIC](22) NULL,
	[INV_RISK_LVL_TUP] [NUMERIC](22) NULL,
	[AVG_INV_RISK_LVL_CALC] [NUMERIC](22) NULL,
	[APP_DECLAR_CHOICE] [NVARCHAR](1) NULL,
	[PRE_RECORD] [NVARCHAR](1) NULL,
	[VULNERABLE_CUST] [NVARCHAR](1) NULL,
	[CALL_CUST] [NVARCHAR](1) NULL,
	[RISK_RPT_DATE] [DATE] NULL,
	[VULNERABLE_REASN] [NVARCHAR](20) NULL,
	[PRODUCT_SUITABLE] [NVARCHAR](1) NULL,
	[PREMIUM_SUITABLE] [NVARCHAR](1) NULL,
	[BROKER_SKIP] [NVARCHAR](1) NULL,
	[CALL_CUST_UPD_DT] [DATE] NULL,
	[PREMIUM_TERM_SUITABLE] [NVARCHAR](1) NULL,
	[MACAU_IND] [NVARCHAR](1) NULL,
	[TRXN_DT] [DATE] NULL,
	[TRXN_TYP] [NVARCHAR](10) NOT NULL,
	[SEQ_NUM] [NVARCHAR](14) NULL,
	[AGT_CODE] [NVARCHAR](6) NULL,
	[ILAS_IND] [NVARCHAR](1) NULL,
	[VC_IND] [NVARCHAR](1) NULL,
	[FNA_EXEMPT_IND] [NVARCHAR](1) NULL,
	[FNA_MISMATCH_IND] [NVARCHAR](1) NULL,
	[FNA_Q1_RESULT] [NVARCHAR](1) NULL,
	[FNA_Q2_RESULT] [NVARCHAR](1) NULL,
	[FNA_Q3_RESULT] [NVARCHAR](1) NULL,
	[FNA_COMBINED_RESULT] [NVARCHAR](1) NULL,
	[INSUR_OPT_IND] [NVARCHAR](1) NULL,
	[PMT_MODE] [NVARCHAR](5) NULL,
	[FNA_Q5_SP_IND] [NVARCHAR](1) NULL,
	[COOL_OFF_LEVY] [NVARCHAR](1) NULL,
	[VR_OPT_OUT_IND] [NVARCHAR](1) NULL,
	[TARGET_CUST_MISMATCH] [NVARCHAR](1) NULL,
	[SINGLE_PREM_IND] [NVARCHAR](1) NULL,
	[SINGLE_PREM_AMT] [NUMERIC](22) NULL,
	[SINGLE_PREM_CUR] [NVARCHAR](3) NULL,
	[INIT_SUBSCPT_AMT] [NUMERIC](22) NULL,
	[INIT_SUBSCPT_CUR] [NVARCHAR](3) NULL,
	[REGL_SUBSCPT_AMT] [NUMERIC](22) NULL,
	[REGL_SUBSCPT_CUR] [NVARCHAR](3) NULL,
	[PAYOUT_IC] [NVARCHAR](1) NULL,
	[IS_MEDICAL_PLAN] [NVARCHAR](1) NULL,
	[IS_AMCM_PSC] [NVARCHAR](1) NULL,
	[REFERRAL_PROGRAM] [NVARCHAR](1) NULL,
	[SALESMAN_IND] [NVARCHAR](1) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_ILAS_RESULT] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO