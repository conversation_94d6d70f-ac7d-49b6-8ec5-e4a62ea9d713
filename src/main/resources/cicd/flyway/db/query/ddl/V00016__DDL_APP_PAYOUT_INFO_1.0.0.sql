------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_PAYOUT_INFO](
	[POLICY_KEY] [NVARCHA<PERSON>](18) NOT NULL,
	[SYS_CODE] [NVARCHAR](3) NULL,
	[CONTRACT_NO] [NVARCHA<PERSON>](10) NULL,
    [SUB_GROUP_NO] [NVARCHAR](5) NULL,
    [CERTIFICATE_NO] [NVARCHAR](10) NULL,
    [PAYO_MODE] [NVARCHAR](5) NULL,
    [PAYO_AMT] [NUMERIC](22) NULL,
    [PAYO_CRCY] [NVARCHAR](2) NULL,
    [PAYO_MTHD] [NVARCHAR](10) NULL,
    [PAYO_ARANG] [NVARCHAR](4) NULL,
    [PAYO_ACCOUNT_ROLE] [NVARCHAR](1) NULL,
    [PAYO_ACCOUNT_NO] [NVARCHAR](21) NULL,
    [DEF_PAYO_MTHD] [NVARCHAR](5) NULL,
    [DEF_PAYO_CRCY] [NVARCHAR](2) NULL,
    [FPS_ID_TYP] [NVARCHAR](10) NULL,
    [FPS_ID_COUNTRY_CD] [NVARCHAR](5) NULL,
    [FPS_ID_MOBILE_NO] [NVARCHAR](18) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_PAYOUT_INFO] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO