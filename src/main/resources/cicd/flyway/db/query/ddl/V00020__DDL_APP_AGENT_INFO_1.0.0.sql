------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_AGENT_INFO](
	[POLICY_KEY] [NVARCHA<PERSON>](18) NOT NULL,
	[SYS_CODE] [NVA<PERSON>HA<PERSON>](3) NULL,
	[CONTRACT_NO] [NVA<PERSON>HA<PERSON>](10) NULL,
	[SUB_GROUP_NO] [NVARCHAR](5) NULL,
	[CERTIFICATE_NO] [NVARCHAR](10) NULL,
	[AGENT_CODE] [NVARCHAR](6) NOT NULL,
	[SPLIT] [NUMERIC](22) NULL,
	[SERVICING_AGENT] [NVARCHAR](1) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_AGENT_INFO] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC,
	[AGENT_CODE] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

--CREATE NONCLUSTERED INDEX POLICY_KEY_INDEX ON APP_AGENT_INFO(POLICY_KEY)
--GO
--
--CREATE SEQUENCE [dbo].[SEQ_APP_AGENT_INFO] AS [NUMERIC](10) START WITH 1 INCREMENT BY 1 CACHE 50
--GO