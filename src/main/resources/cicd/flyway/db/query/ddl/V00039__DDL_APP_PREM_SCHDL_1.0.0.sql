------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL05_NBUW_7642
-- Mod. Desc : Data Capture & Update - UL/HNW (Part 2)
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_PREM_SCHDL](
	[POLICY_KEY] [NVARCHAR](18) NOT NULL,
	[SYS_CODE] [NVARCHAR](3) NOT NULL,
	[CONTRACT_NO] [NVARCHAR](20) NOT NULL,
	[SUB_GROUP_NO] [NVARCHAR](5) NOT NULL,
	[CERTIFICATE_NO] [NVARCHAR](10) NOT NULL,
	[SCH_TYPE] [NVARCHAR](1) NOT NULL,
	[FR_POL_YR] [NUMERIC](3) NOT NULL,
	[TO_POL_YR] [NUMERIC](3) NULL,
	[PLAN_PREM] [NUMERIC](11,2) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATETIME] NULL
 CONSTRAINT [PK_APP_PREM_SCHDL] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC,
	[SCH_TYPE] ASC,
	[FR_POL_YR] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO