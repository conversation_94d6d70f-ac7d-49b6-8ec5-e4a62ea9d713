------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_PAYMENT_INFO](
	[POLICY_KEY] [NVARCHA<PERSON>](18) NOT NULL,
	[SYS_CODE] [NVARCHAR](3) NULL,
	[CONTRACT_NO] [NVARCHAR](10) NULL,
    [SUB_GROUP_NO] [NVARCHAR](5) NULL,
    [CERTIFICATE_NO] [NVARCHAR](10) NULL,
    [PAYMENT_METHOD] [NVARCHAR](1) NULL,
    [PDF_OPTION] [NVARCHAR](1) NULL,
    [PDF_PREMIUM_OFFSET_DATE] [DATE] NULL,
    [PDF_INTEREST] [NUMERIC](22,2) NULL,
    [PDF_INTEREST_INDICATOR] [NVARCHAR](1) NULL,
    [PDF_AMOUNT] [NUMERIC](22,2) NULL,
    [PDF_NO_OF_INSTALMENT] [NUMERIC](22) NULL,
    [PDF_DEFER_TO_YEAR] [NVARCHAR](4) NULL,
    [PDF_NO_OF_PREPAYMENT] [NUMERIC](22) NULL,
    [PDF_COVERAGE_OPTION] [NVARCHAR](1) NULL,
    [PDF_PREMIUM_DUMP_IN_DATE] [DATE] NULL,
    [ACCOUNT_HOLDER_NAME] [NVARCHAR](30) NULL,
    [ACCOUNT_NO] [NVARCHAR](20) NULL,
    [PAYMENT_DATE] [NVARCHAR](2) NULL,
    [AUTOPAY_EFF_DATE] [NVARCHAR](6) NULL,
    [DDA_REJECT_REASON] [NVARCHAR](1) NULL,
    [COUPON_IND] [NVARCHAR](1) NULL,
    [CARD_EXPRY_DATE] [NVARCHAR](4) NULL,
    [AUTOPAY_TOPUP_PREM] [NUMERIC](22) NULL,
    [INIT_AUTO_PREM_IND] [NVARCHAR](1) NULL,
    [INIT_AUTO_PREM_DATE] [DATE] NULL,
    [PDF_PREM_DISC_AMT] [NUMERIC](22,2) NULL,
    [PDF_LEVY_DISC_AMT] [NUMERIC](22,2) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_PAYMENT_INFO] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO