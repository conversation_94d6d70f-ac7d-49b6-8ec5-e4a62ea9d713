------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_CLIENT_ROLE_INFO](
	[POLICY_KEY] [NVARCHAR](18) NOT NULL,
	[CLIENT_ROLE] [NVARCHAR](3) NOT NULL,
	[CONTRACT_NO] [NVARCHAR](10) NULL,
	[RESIDE_HK_FLG] [NVARCHAR](3) NULL,
	[MTHLY_INCOME] [NUMERIC](22) NULL,
	[MTHLY_INCOME_CURR] [NVARCHAR](2) NULL,
	[OCCUPATION] [NVARCHAR](5) NULL,
	[OTHER_OCCUPATION] [NVARCHAR](40) NULL,
	[INSURED_RELATIONSHIP] [NVARCHAR](3) NULL,
	[INSURED_RELATIONSHIP_DESC] [NVARCHAR](50) NULL,
	[MACAU_ID] [NVARCHAR](1) NULL,
	[NATIONALITY] [NVARCHAR](2) NULL,
	[COMPANY_OWNER_SM] [NVARCHAR](3) NULL,
	[AML_OCCUP] [NVARCHAR](3) NULL,
	[ID_DOC_TYP] [NVARCHAR](2) NULL,
	[DOB_COUNTRY] [NVARCHAR](2) NULL,
    [OFFICE_COUNTRY] [NVARCHAR](2) NULL,
    [CITIZEN_COUNTRY_1] [NVARCHAR](2) NULL,
    [CITIZEN_COUNTRY_2] [NVARCHAR](2) NULL,
    [CITIZEN_COUNTRY_3] [NVARCHAR](2) NULL,
    [CITIZEN_COUNTRY_4] [NVARCHAR](2) NULL,
    [CITIZEN_COUNTRY_5] [NVARCHAR](2) NULL,
    [CITIZEN_COUNTRY_6] [NVARCHAR](2) NULL,
    [RES_CITY] [NVARCHAR](5) NULL,
    [US_SELF_CERT] [NVARCHAR](1) NULL,
    [OWN_PCT] [NUMERIC](22) NULL,
    [REMARKS] [NVARCHAR](30) NULL,
    [PERM_COUNTRY] [NVARCHAR](2) NULL,
    [PERM_COUNTRY_OTHER] [NVARCHAR](200) NULL,
    [MCV_APP_IND] [NVARCHAR](2) NULL,
    [MCV_REF_NO] [NVARCHAR](15) NULL,
    [MCV_APPROVE_STATUS] [NVARCHAR](2) NULL,
    [MCV_AWD_RIP_IND] [NVARCHAR](1) NULL,
    [EDU_LEVEL] [NVARCHAR](2) NULL,
    [IFS_MP_IND] [NVARCHAR](2) NULL,
    [CRS_ADDR1] [NVARCHAR](160) NULL,
    [CRS_ADDR2] [NVARCHAR](160) NULL,
    [CRS_ADDR3] [NVARCHAR](160) NULL,
    [CRS_COUNTRY] [NVARCHAR](3) NULL,
    [LEGAL_ID_COUNTRY] [NVARCHAR](3) NULL,
    [CRS_SELF_CERT] [NVARCHAR](3) NULL,
    [BIRTH_CITY] [NVARCHAR](50) NULL,
    [CRS_CITY] [NVARCHAR](280) NULL,
    [PSC_EMAIL] [NVARCHAR](60) NULL,
    [PSC_MOBILE_NO] [NVARCHAR](18) NULL,
    [PSC_RESID_NO] [NVARCHAR](18) NULL,
    [PSC_OFFICE_NO] [NVARCHAR](18) NULL,
    [PSC_OFFICE_EXT] [NVARCHAR](4) NULL,
    [PSC_CONFIRM] [NVARCHAR](1) NULL,
    [PSC_MOBILE_COUNTRY] [NVARCHAR](2) NULL,
    [PSC_RESID_COUNTRY] [NVARCHAR](2) NULL,
    [PSC_OFFICE_COUNTRY] [NVARCHAR](2) NULL,
    [OFFICE_CITY] [NVARCHAR](280) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_CLIENT_ROLE_INFO] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC,
	[CLIENT_ROLE] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO