------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_UW_RESULT](
	[POLICY_KEY] [NVARCHA<PERSON>](18) NOT NULL,
	[CONTRACT_NO] [NVARCHAR](10) NULL,
    [CASE_NO] [NVARCHAR](20) NULL,
    [NBWB_RETRIEVE_DATE] [DATE] NULL,
    [STATUS] [NVARCHAR](40) NULL,
    [CAMPG_CODE] [NVARCHAR](110) NULL,
    [UW_CLASS] [NVARCHAR](2) NULL,
    [OCC_CODE] [NVARCHAR](6) NULL,
    [SKIP_SOS_IND] [NVARCHAR](1) NULL,
    [INSURED_MORTALITY] [NVARCHAR](4) NULL,
    [IMPAIRMENT_CD1] [NVARCHAR](6) NULL,
    [IMPAIRMENT_CD2] [NVARCHAR](6) NULL,
    [IMPAIRMENT_CD3] [NVARCHAR](6) NULL,
    [INSURED_MORBIDITY] [NVARCHAR](4) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_UW_RESULT] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO