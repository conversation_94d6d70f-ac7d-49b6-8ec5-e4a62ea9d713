------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_CLIENT_ROLE_LINK](
	[POLICY_KEY] [NVARCHAR](18) NOT NULL,
	[CONTRACT_NO] [NVARCHAR](10) NULL,
	[PARTY_TYPE] [NVARCHAR](1) NULL,
	[CLIENT_ID] [NVARCHAR](22) NOT NULL,
	[CLIENT_ROLE] [NVARCHAR](3) NOT NULL,
	[BASIC_INSURED_FLAG] [NVARCHAR](1) NULL,
	[LOB] [NVARCHAR](1) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_CLIENT_ROLE_LINK] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC,
	[CLIENT_ID] ASC,
	[CLIENT_ROLE] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO