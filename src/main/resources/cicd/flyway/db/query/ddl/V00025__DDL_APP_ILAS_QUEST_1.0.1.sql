------------------------------------------------------------------------------------------
-- Mod. Date : 08 Aug 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL05_NBUW_1903
-- Mod. Desc : [DEV] Data Update - FNA(HK) & IFSPF
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

ALTER TABLE [DBO].[APP_ILAS_QUEST] DROP CONSTRAINT [PK_APP_ILAS_QUEST]
GO

CREATE NONCLUSTERED INDEX IDX1_APP_ILAS_QUEST
ON APP_ILAS_QUEST
    (POLICY_KEY ASC)
GO

ALTER TABLE [DBO].[APP_ILAS_QUEST] ALTER COLUMN SEQ_NO [NUMERIC](22) NOT NULL;
ALTER TABLE [DBO].[APP_ILAS_QUEST] ALTER COLUMN ANSWER [NVARCHAR](30) NOT NULL
GO

ALTER TABLE [DBO].[APP_ILAS_QUEST] ADD CONSTRAINT
[PK_APP_ILAS_QUEST] PRIMARY KEY CLUSTERED(
	[POLICY_KEY] ASC,
	[SEQ_NO] ASC,
	[ANSWER] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
GO
