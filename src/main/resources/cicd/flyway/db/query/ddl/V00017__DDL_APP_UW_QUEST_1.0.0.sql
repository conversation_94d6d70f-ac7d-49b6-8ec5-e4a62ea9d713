------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_UW_QUEST](
	[POLICY_KEY] [NVARCHA<PERSON>](18) NOT NULL,
	[SYS_CODE] [NVA<PERSON>HA<PERSON>](3) NULL,
	[CONTRACT_NO] [NVA<PERSON>HA<PERSON>](10) NULL,
    [SUB_GROUP_NO] [NVARCHAR](5) NULL,
    [CERTIFICATE_NO] [NVARCHAR](10) NULL,
    [OCCUPATION_CHANGED] [NVARCHAR](1) NULL,
    [RESIDENCE_CHANGED] [NVARCHAR](1) NULL,
    [RATED_DECLINED_BEFORE] [NVARCHAR](1) NULL,
    [HAZARDOUS_ACTIVITY] [NVARCHAR](1) NULL,
    [SPECIAL_REQUEST_UW] [NVARCHAR](1) NULL,
    [HB_MEDICAL_QUEST] [NVARCHAR](1) NULL,
    [HP_MEDICAL_QUEST] [NVARCHAR](1) NULL,
    [WEIGHT_LB] [NUMERIC](22) NULL,
    [WEIGHT_KG] [NUMERIC](22) NULL,
    [HEIGHT_FT] [NUMERIC](22) NULL,
    [HEIGHT_IN] [NUMERIC](22) NULL,
    [HEIGHT_M] [NUMERIC](22) NULL,
    [FAMILY_HISTORY] [NVARCHAR](1) NULL,
    [PRE_DEF_DISEASE] [NVARCHAR](1) NULL,
    [MEDICAL_HISTORY] [NVARCHAR](2) NULL,
    [PRE_DEF_IMPAIRMENT] [NVARCHAR](1) NULL,
    [LAST_CONSULTATION] [NVARCHAR](2) NULL,
    [BIRTH_WEIGHT_LB] [NUMERIC](22) NULL,
    [BIRTH_WEIGHT_OZ] [NUMERIC](22) NULL,
    [BIRTH_WEIGHT_KG] [NUMERIC](22) NULL,
    [HOSP_OVER_5DAY] [NVARCHAR](1) NULL,
    [CIGARETTE_PER_DAY] [NUMERIC](22) NULL,
    [WEIGHT_GAIN_BELOW_16] [NVARCHAR](1) NULL,
    [TIA_ISSUED] [NVARCHAR](1) NULL,
    [NET_12MTHS_RISK] [NUMERIC](22) NULL,
    [NET_12MTHS_RISK_CURR] [NVARCHAR](3) NULL,
    [CAB_12MTHS_RISK] [NUMERIC](22) NULL,
    [CAB_12MTHS_RISK_CURR] [NVARCHAR](3) NULL,
    [NET_RISK] [NUMERIC](22) NULL,
    [NET_RISK_CURR] [NVARCHAR](3) NULL,
    [CAB_RISK] [NUMERIC](22) NULL,
    [CAB_RISK_CURR] [NVARCHAR](3) NULL,
    [MDB_RISK] [NUMERIC](22) NULL,
    [MDB_RISK_CURR] [NVARCHAR](3) NULL,
    [ACB_RISK] [NUMERIC](22) NULL,
    [ACB_RISK_CURR] [NVARCHAR](3) NULL,
    [ADB_RISK] [NUMERIC](22) NULL,
    [ADB_RISK_CURR] [NVARCHAR](3) NULL,
    [DI_RISK] [NUMERIC](22) NULL,
    [DI_RISK_CURR] [NVARCHAR](3) NULL,
    [HI_RISK] [NUMERIC](22) NULL,
    [HI_RISK_CURR] [NVARCHAR](3) NULL,
    [LAST_CONSULTATION_DESC] [NVARCHAR](100) NULL,
    [MEDICAL_HISTORY_DESC] [NVARCHAR](50) NULL,
    [HB_MEDICAL_QUEST_DESC] [NVARCHAR](50) NULL,
    [HP_MEDICAL_QUEST_DESC] [NVARCHAR](50) NULL,
    [TIA_ISSUED_DESC] [NVARCHAR](50) NULL,
    [PRE_DEF_IMPAIRMENT_DESC] [NVARCHAR](50) NULL,
    [PAYOR_OCCUPATION] [NVARCHAR](5) NULL,
    [PAYOR_OCC_RES_CHG] [NVARCHAR](5) NULL,
    [PAYOR_MACAU_ID] [NVARCHAR](1) NULL,
    [PAYOR_WEIGHT_LB] [NUMERIC](22) NULL,
    [PAYOR_WEIGHT_KG] [NUMERIC](22) NULL,
    [PAYOR_HEIGHT_FT] [NUMERIC](22) NULL,
    [PAYOR_HEIGHT_IN] [NUMERIC](22) NULL,
    [PAYOR_HEIGHT_M] [NUMERIC](22) NULL,
    [PAYOR_MEDICAL_HISTORY] [NVARCHAR](2) NULL,
    [PAYOR_MEDICAL_HIST_DESC] [NVARCHAR](50) NULL,
    [PAYOR_PRE_DEF_IMPAIRMENT] [NVARCHAR](2) NULL,
    [PAYOR_PRE_DEF_IMPAIRMENT_DESC] [NVARCHAR](50) NULL,
    [PAYOR_LAST_CONSULTATION] [NVARCHAR](2) NULL,
    [PAYOR_LAST_CON_DESC] [NVARCHAR](100) NULL,
    [PAYOR_FAMILY_HISTORY] [NVARCHAR](1) NULL,
    [PAYOR_PRE_DEF_DISEASE] [NVARCHAR](1) NULL,
    [PAYOR_CIGARETTE_PER_DAY] [NUMERIC](22) NULL,
    [PAYOR_RATED_DECLINED_BEF] [NVARCHAR](1) NULL,
    [PAYOR_HAZARDOUS_ACT] [NVARCHAR](1) NULL,
    [MX_MEDICAL_QUEST] [NVARCHAR](2) NULL,
    [MX_MEDICAL_QUEST_DESC] [NVARCHAR](50) NULL,
    [ALCOHOL_PER_WEEK] [NUMERIC](22) NULL,
    [PAYOR_ALCOHOL_PER_WEEK] [NUMERIC](22) NULL,
    [OTH_INS_MORE_BMI_LIMIT] [NVARCHAR](1) NULL,
    [OTH_INS_MORE_BMI_LIMIT_D] [NVARCHAR](50) NULL,
    [OTH_INS_MORE_CIGARETTE_LIMIT] [NVARCHAR](1) NULL,
    [OTH_INS_MORE_CIGARETTE_LIMIT_D] [NVARCHAR](50) NULL,
    [OTH_INS_MORE_ALCOHOL_LIMIT] [NVARCHAR](1) NULL,
    [OTH_INS_MORE_ALCOHOL_LIMIT_D] [NVARCHAR](50) NULL,
    [OTH_INS_FAMILY_HISTORY] [NVARCHAR](1) NULL,
    [OTH_INS_FAMILY_HISTORY_D] [NVARCHAR](50) NULL,
    [OTH_INS_MEDICAL_HISTORY] [NVARCHAR](2) NULL,
    [OTH_INS_MEDICAL_HISTORY_D] [NVARCHAR](50) NULL,
    [OTH_INS_IMPAIRMENT] [NVARCHAR](2) NULL,
    [OTH_INS_IMPAIRMENT_D] [NVARCHAR](50) NULL,
    [OTH_INS_MISC] [NVARCHAR](1) NULL,
    [OTH_INS_MISC_D] [NVARCHAR](50) NULL,
    [FAMILY_HISTORY_DESC] [NVARCHAR](50) NULL,
    [PAYOR_FAMILY_HISTORY_DESC] [NVARCHAR](50) NULL,
    [SIMP_UND_ME_M] [NVARCHAR](1) NULL,
    [SIMP_UND_ME_M_D] [NVARCHAR](50) NULL,
    [NEW_BORN_HOSP_6DAY] [NVARCHAR](1) NULL,
    [NUM_SMOKE_YR] [NUMERIC](22) NULL,
    [PAYOR_NUM_SMOKE_YR] [NUMERIC](22) NULL,
    [ADDB_RISK] [NUMERIC](22) NULL,
    [DIS_RISK] [NUMERIC](22) NULL,
    [ADDB_RISK_CURR] [NVARCHAR](3) NULL,
    [DIS_RISK_CURR] [NVARCHAR](3) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATE] NULL
 CONSTRAINT [PK_APP_UW_QUEST] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO