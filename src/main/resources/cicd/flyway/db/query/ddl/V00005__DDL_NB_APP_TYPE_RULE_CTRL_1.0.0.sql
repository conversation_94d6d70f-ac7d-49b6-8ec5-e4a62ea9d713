------------------------------------------------------------------------------------------
-- Mod. Date : 26 July 2021
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL05_SB_NB_19008
-- Mod. Desc : UW Class system auto-determination
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_APP_TYPE_RULE_CTRL](
	[CTRL_NAME] [NVARCHAR](50) NOT NULL,
	[PLAN_CODE] [NVA<PERSON>HA<PERSON>](50) NOT NULL,
	[TRANCHE_ID] [NVARCHAR](50) NOT NULL,
	[RIDER] [NVARCHAR](50) NOT NULL,
	[ATTH_RIDER] [NVARCHAR](50) NOT NULL,
	[APP_TYPE] [VARCHAR](10) NOT NULL,
	[CREATED_TIMESTAMP] [DATETIME] NOT NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL,
	[LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
	[LAST_UPDATED_BY] [VARCHAR](50) NULL,
	[DELETED_TIMESTAMP] [DATETIME] NULL,
	[DELETED_BY] [VARCHAR](50) NULL
 CONSTRAINT [PK_NB_APP_TYPE_RULE_CTRL] PRIMARY KEY CLUSTERED 
(
	[CTRL_NAME] ASC,
	[PLAN_CODE] ASC,
	[TRANCHE_ID] ASC,
	[RIDER] ASC,
	[ATTH_RIDER] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO