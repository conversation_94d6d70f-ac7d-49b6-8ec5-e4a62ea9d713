------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_RULE_CTRL](
	[CTRL_NAME] [NVARCHAR](50) NOT NULL,
	[RULE_ID] [NVARCHAR](50) NOT NULL,
	[EXEC_COND_1] [N<PERSON><PERSON><PERSON><PERSON>](50) NOT NULL,
	[EXEC_COND_2] [N<PERSON><PERSON><PERSON><PERSON>](50) NOT NULL,
	[EXEC_COND_3] [NVARCHAR](50) NOT NULL,
	[COND_ORDER] [INT] NOT NULL,
	[EXEC_IND] [VARCHAR](1) NOT NULL,
	[CREATED_TIMESTAMP] [DATETIME] NOT NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL,
	[LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
	[LAST_UPDATED_BY] [VARCHAR](50) NULL,
	[DELETED_TIMESTAMP] [DATETIME] NULL,
	[DELETED_BY] [VARCHAR](50) NULL
 CONSTRAINT [PK_NB_RULE_CTRL] PRIMARY KEY CLUSTERED 
(
	[CTRL_NAME] ASC,
	[RULE_ID] ASC,
	[EXEC_COND_1] ASC,
	[EXEC_COND_2] ASC,
	[EXEC_COND_3] ASC,
	[COND_ORDER] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO