------------------------------------------------------------------------------------------
-- Mod. Date : 11 May 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_7934
-- Mod. Desc : Data Capture & Update - Fund (for ILAS)
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[APP_FUND](
	[POLICY_KEY] [NVARCHAR](18) NOT NULL,
	[SYS_CODE] [NVARCHAR](3) NULL,
	[CONTRACT_NO] [NVARCHAR](10) NULL,
	[SEQ_NO] [NUMERIC](3,0) NOT NULL,
	[FUND_CODE] [NVARCHAR](5) NOT NULL,
	[CURRENCY] [NVARCHAR](2) NULL,
	[DEPOSIT] [NUMERIC](5,2) NULL,
	[RE_BALANCING] [NUMERIC](5,2) NULL,
	[ACCOUNT_TYPE] [NVARCHAR](3) NOT NULL,
	[IIO_OPTION] [NVARCHAR](1) NULL,
	[REBALANCING_INDICATOR] [NVARCHAR](2) NULL,
	[DEAL_BASIS] [NVARCHAR](1) NULL,
	[DEAL_AMT] [NUMERIC](13,2) NULL,
	[LAST_MODIFIED_CHANNEL] [NVARCHAR](10) NULL,
	[LAST_MODIFIED_DATE] [DATETIME] NULL,
	[AS_OF_DATE] [DATETIME] NULL
 CONSTRAINT [PK_APP_FUND] PRIMARY KEY CLUSTERED
(
	[POLICY_KEY] ASC,
	[FUND_CODE] ASC,
	[ACCOUNT_TYPE] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO