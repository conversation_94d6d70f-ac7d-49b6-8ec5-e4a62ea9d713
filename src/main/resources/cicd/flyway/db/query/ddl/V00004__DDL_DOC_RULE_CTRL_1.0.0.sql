------------------------------------------------------------------------------------------
-- Mod. Date : 2 Sep 2021
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_21_RL05_SB_NB_19079
-- Mod. Desc : Document Validation in NBUWS
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[DOC_RULE_CTRL](
	[CTRL_NAME] [NVARCHAR](50) NOT NULL,
	[RULE_ID] [NVARCHA<PERSON>](50) NOT NULL,
	[DOC_TYPE] [N<PERSON><PERSON>HA<PERSON>](500) NOT NULL,
	[RULE_COND] [N<PERSON><PERSON>HA<PERSON>](MAX) NOT NULL,
	[RULE_PARAM] [NVARCHAR](MAX) NOT NULL,
	[RULE_MSG] [NVARCHAR](1000) NOT NULL,
	[EXEC_IND] [VARCHAR](1) NOT NULL,
	[CREATED_TIMESTAMP] [DATETIME] NOT NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL,
	[LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
	[LAST_UPDATED_BY] [VARCHAR](50) NULL,
	[DELETED_TIMESTAMP] [DATETIME] NULL,
	[DELETED_BY] [VARCHAR](50) NULL
 CONSTRAINT [PK_DOC_RULE_CTRL] PRIMARY KEY CLUSTERED 
(
	[CTRL_NAME] ASC,
	[RULE_ID] ASC,
	[DOC_TYPE] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO