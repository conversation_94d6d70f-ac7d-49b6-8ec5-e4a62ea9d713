-------------------------------------------------------------------------------------------
--  Mod. Date : 03 Sep 2024
--  Mod. By   : (MITDC CD) <PERSON>
--  Mod. ref  : IFP_24_RL05_NBUW_27532
--  Mod. Desc : 2024 Jan Release - Support RP ILAS Day 2 items in NBUWS
-------------------------------------------------------------------------------------------
 -- Mod. Date : 24 Oct 2024
 -- Mod. By   : (MITDC CD) Jason Zhang
 -- Mod. ref  : IFP_25_RL01_NBUW_30056
 -- Mod. Desc : Support Quick App in NBUWS Currency
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'CHK_PHI_TOT_RISK_LIMIT';

insert into sys_config values(
    'CHK_PHI_TOT_RISK_LIMIT',
    CAST(N'[' AS nvarchar(max))
        -- 20241025 IFP_25_RL01_NBUW_30056 update start
		--+N'{"planCode":"MT110","schmCode":"*","chkType": "INS_TAAR_24MONTHS_RISK_LIMIT","upperPremLimit": "500000","appType":"S"}'
        --+N',{"planCode":"MT115","schmCode":"*","chkType": "INS_TAAR_24MONTHS_RISK_LIMIT","upperPremLimit": "500000","appType":"S"}'
        --+N',{"planCode":"MT120","schmCode":"*","chkType": "INS_TAAR_24MONTHS_RISK_LIMIT","upperPremLimit": "500000","appType":"S"}'
		+N'{"planCode":"MT110","schmCode":"*","chkType": "INS_TAAR_24MONTHS_RISK_LIMIT","upperPremLimit": "500000","appType":"Q"}'
        +N',{"planCode":"MT115","schmCode":"*","chkType": "INS_TAAR_24MONTHS_RISK_LIMIT","upperPremLimit": "500000","appType":"Q"}'
        +N',{"planCode":"MT120","schmCode":"*","chkType": "INS_TAAR_24MONTHS_RISK_LIMIT","upperPremLimit": "500000","appType":"Q"}'
        -- 20241025 IFP_25_RL01_NBUW_30056 update end
    +N']',
    '03-Nov-2024',
	'SYSTEM',
	'05-Jan-2025');

commit;