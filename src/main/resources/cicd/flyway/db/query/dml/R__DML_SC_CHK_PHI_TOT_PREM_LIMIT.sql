-------------------------------------------------------------------------------------------
--  Mod. Date : 01 Dec 2023
--  Mod. By   : (MITDC CD) <PERSON>
--  Mod. ref  : IFP_24_RL01_NBUW_12952
--  Mod. Desc : 2024 Jan Release - NBUWS validation for large amount validation (Per-life checking)
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'CHK_PHI_TOT_PREM_LIMIT';

insert into sys_config values(
    'CHK_PHI_TOT_PREM_LIMIT',
    CAST(N'[' AS nvarchar(max))
		+N'{"chkType": "OWN_TOT_PREM_LIMIT","lowerPremLimit": "500000","lowerPremLimitDesc": "500K","upperPremLimit": "1000000","upperPremLimitDesc": "1M"}'
        +N',{"chkType": "GIO_TOT_PREM_LIMIT","lowerPremLimit": "0","lowerPremLimitDesc": "500K","upperPremLimit": "1000000","upperPremLimitDesc": "1M"}'
        +N',{"chkType": "GIO_TOT_PREM_NHQ_LIMIT","lowerPremLimit": "1","upperPremLimit": "4000000","lowerAgeLimit":"66","upperAgeLimit":"200"}'
        +N',{"chkType": "GIO_TOT_PREM_BRK_LIMIT","lowerPremLimit": "10000001","upperPremLimit": "25000000","lowerAgeLimit":"18","upperAgeLimit":"50"}'
        +N',{"chkType": "GIO_TOT_PREM_BRK_LIMIT","lowerPremLimit": "4000001","upperPremLimit": "5000000","lowerAgeLimit":"66","upperAgeLimit":"200"}'
        +N',{"chkType": "GIO_TOT_PREM_SIO_LIMIT","lowerPremLimit": "5000001","upperPremLimit": "25000000","lowerAgeLimit":"51","upperAgeLimit":"65"}'
        +N',{"chkType": "GIO_TOT_PREM_SIO_LIMIT","lowerPremLimit": "5000001","upperPremLimit": "20000000","lowerAgeLimit":"66","upperAgeLimit":"200"}'
        +N',{"chkType": "GIO_TOT_PREM_HD_LIMIT","lowerPremLimit": "25000001","upperPremLimit": "999999999999","lowerAgeLimit":"18","upperAgeLimit":"65"}'
        +N',{"chkType": "GIO_TOT_PREM_HD_LIMIT","lowerPremLimit": "20000001","upperPremLimit": "999999999999","lowerAgeLimit":"66","upperAgeLimit":"200"}'
        +N',{"chkType": "GIO_TOT_PREM_IC_LIMIT","lowerPremLimit": "7500001","upperPremLimit": "999999999999","lowerAgeLimit":"0","upperAgeLimit":"17"}'
        +N',{"chkType": "PP_TOT_PREM_APP_CHK","lowerPremLimit": "1000001","upperPremLimit": "1500000","appType":"G"}'
        +N',{"chkType": "PP_TOT_PREM_LIMIT","lowerPremLimit": "1500001","upperPremLimit": "999999999999"}'
    +N']',
    '07-Jan-2024',
	'SYSTEM',
	'07-Jan-2024');

commit;