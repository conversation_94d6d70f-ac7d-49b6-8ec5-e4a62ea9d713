------------------------------------------------------------------------------------------
-- Mod. Date : 3 Mar 2020
-- Mod. By   : <PERSON>era EG
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
------------------------------------------------------------------------------------------
-- Mod. Date : 27 Dec 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_23_RL02_NBUW_2349
-- Mod. Desc : Data Update - Basic Info - Family Benefit, Compassionate Premium Waiver
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'FMLY_BNFTS_RELATION';

--IFP_23_RL02_NBUW_2349 start
--insert into list_of_value values('FMLY_BNFTS_RELATION',N'[{"code":"FFA","engDesc":"Father","chinDesc":"父親","attributes":[{"nbwbCode":"FFA","eposValue":"FFA"}]},
--{"code":"FMO","engDesc":"Mother","chinDesc":"母親","attributes":[{"nbwbCode":"FMO","eposValue":"FMO"}]},
--{"code":"FSO","engDesc":"Son","chinDesc":"兒子","attributes":[{"nbwbCode":"FSO","eposValue":"FSO"}]},
--{"code":"FDA","engDesc":"Daughter","chinDesc":"女兒","attributes":[{"nbwbCode":"FDA","eposValue":"FDA"}]},
--{"code":"FPH","engDesc":"Policyowner husband","chinDesc":"保單持有人之丈夫","attributes":[{"nbwbCode":"FPH","eposValue":"FPH"}]},
--{"code":"FPW","engDesc":"Policyowner wife","chinDesc":"保單持有人之妻子","attributes":[{"nbwbCode":"FPW","eposValue":"FPW"}]}]'
--,'08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');
insert into list_of_value values('FMLY_BNFTS_RELATION',
   CAST(N'[{"code":"FFA","engDesc":"Father","chinDesc":"父親","attributes":[{"nbwbCode":"FFA","eposValue":"FFA"}]}' AS nvarchar(max))
  +N',{"code":"FMO","engDesc":"Mother","chinDesc":"母親","attributes":[{"nbwbCode":"FMO","eposValue":"FMO"}]}'
  +N',{"code":"FSO","engDesc":"Son","chinDesc":"兒子","attributes":[{"nbwbCode":"FSO","eposValue":"FSO"}]}'
  +N',{"code":"FDA","engDesc":"Daughter","chinDesc":"女兒","attributes":[{"nbwbCode":"FDA","eposValue":"FDA"}]}'
  +N']'
,'23-APR-2023',NULL,'SYSTEM','23-APR-2023');
--IFP_23_RL02_NBUW_2349 end

commit;
