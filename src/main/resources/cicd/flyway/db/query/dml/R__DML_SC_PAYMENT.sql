------------------------------------------------------------------------------------------
-- Mod. Date : 17 Nov 2021
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_22_RL01_SB_NB_22681
-- Mod. Desc : [DEV] PYD, Sufficient Amount & Overpayment
-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Oct 2022
-- Mod. By   : <PERSON> Chen
-- Mod. ref  : IFP_22_RL05_NBUW_3831
-- Mod. Desc : Show overpayment in NBWB(Trad. Product)
-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Apr 2023
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_23_RL04_NBUW_7176
-- Mod. Desc : 2023 Jul Release - Data Capture & Update - Plan and Coverage (ILAS)
-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2022
-- Mod. By   : (MITDC CD) Ivan Wang
-- Mod. ref  : IFP_23_RL05_NBUW_7642
-- Mod. Desc : 2023 Nov Release - Data Capture & Update - UL/HNW (Part 2)
-------------------------------------------------------------------------------------------
-- Mod. Date : 14 Mar 2024
-- Mod. By   : (MITDC CD) Ives Wang
-- Mod. ref  : IFP_24_RL02_NBUW_14146_I
-- Mod. Desc : 2024 Apr Release - Support WIOP3 in NB2.0
-------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) Bean Wang
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------

-- 20221025 IFP_22_RL05_NBUW_3831 added start
-- merge IA_LEVY_CAP and OVER_PAY_TOL to PAYMENT, the below two config can remove in next sprint
-- delete from SYS_CONFIG where CONFIG_CODE = 'IA_LEVY_CAP';
-- delete from SYS_CONFIG where CONFIG_CODE = 'OVER_PAY_TOL';
delete from SYS_CONFIG where CONFIG_CODE = 'PAYMENT';

-- insert into sys_config values('IA_LEVY_CAP','[{"crcyCode":"02","levyCap":"12.5"},{"crcyCode":"04","levyCap":"100"}]','07-MAR-2021','SYSTEM','07-MAR-2021');
-- IFP_23_RL04_NBUW_7176 start
--insert into sys_config values(
--    'PAYMENT',
--    '[
--        {"currency":"02","levyCap":"12.5","overPayTol":"650"},
--        {"currency":"04","levyCap":"100", "overPayTol":"5000"}
--     ]',
--     '07-Mar-2021',
--     'SYSTEM',
--     '06-NOV-2022'
--);
insert into sys_config values(
    'PAYMENT',
    CAST(N'['AS nvarchar(max))
        +N'{"currency":"02","levyCap":"12.5","overPayTol":"650","planCategory":"traditional","toleranceRate":"0.04","limit":"0"}'
        +N',{"currency":"04","levyCap":"100", "overPayTol":"5000","planCategory":"traditional","toleranceRate":"0.04","limit":"0"}'
        +N',{"currency":"04","levyCap":"0", "overPayTol":"0","planCategory":"ari","toleranceRate":"0","limit":"8"}'
        +N',{"currency":"02","levyCap":"0", "overPayTol":"0","planCategory":"ari","toleranceRate":"0","limit":"1"}'
        -- 20230725 --IFP_23_RL05_NBUW_7642 start
        +N',{"currency":"02","levyCap":"12.5", "overPayTol":"1","planCategory":"ul","toleranceRate":"0","limit":"35"}'
        +N',{"currency":"02","levyCap":"0", "overPayTol":"1","planCategory":"wiop2","toleranceRate":"0","limit":"35"}'
        -- 20230725 --IFP_23_RL05_NBUW_7642 end
        --IFP_24_RL02_NBUW_14146_I Start
        +N',{"currency":"02","levyCap":"0", "overPayTol":"1","planCategory":"wiop3","toleranceRate":"0","limit":"35"}'
        --IFP_24_RL02_NBUW_14146_I end
        --IFP_24_RL04_NBUW_23637 added start
        +N',{"currency":"02","levyCap":"0", "overPayTol":"10","planCategory":"apl","toleranceRate":"0","limit":"1"}'
        --IFP_24_RL04_NBUW_23637 added end
     +N']',
     '07-Mar-2021',
     'SYSTEM',
     '08-Sep-2024'
);
-- IFP_23_RL04_NBUW_7176 end
-- 20221025 IFP_22_RL05_NBUW_3831 added end
commit;
