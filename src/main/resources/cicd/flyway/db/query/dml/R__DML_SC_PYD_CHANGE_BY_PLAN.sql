-------------------------------------------------------------------------------------------
 -- Mod. Date : 19 Apr 2023
 -- Mod. By   : (MITDC CD) <PERSON> Hu
 -- Mod. ref  : IFP_23_RL04_NBUW_7176
 -- Mod. Desc : 2023 Jul Release - Data Capture & Update - Plan and Coverage (ILAS)
 -- changeType = A : If App Sign Date falls on 29/30/31 March, PYD moves to 28 Mar,
 -- If App Sign Date falls on 29/30/31 (except March), PYD moves to 1 of next month
 -- changeType = B : If Issue Date falls on 29/30/31, PYD moves to 28 of current month(i.e. month of Issue Date)
 -- changeType = C : If Issue Date falls on 29/30/31, PYD moves to 1 of next month
--------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2022
-- Mod. By   : (MITDC CD) Ivan Wang
-- Mod. ref  : IFP_23_RL05_NBUW_7642
-- Mod. Desc : 2023 Nov Release - Data Capture & Update - UL/HNW (Part 2)
-------------------------------------------------------------------------------------------
 -- Mod. Date : 27 Sep 2023
 -- Mod. By   : (MITDC CD) Darnell Wu
 -- Mod. ref  : IFP_23_RL05_NBUW_11285
 -- Mod. Desc : 2023 Nov Release - Support HK VHIS Rider
--------------------------------------------------------------------------------------------
-- Mod. Date : 14 Mar 2024
-- Mod. By   : (MITDC CD) Ives Wang
-- Mod. ref  : IFP_24_RL02_NBUW_14146_I
-- Mod. Desc : 2024 Apr Release - Support WIOP3 in NB2.0
-------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) Bean Wang
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'PYD_CHANGE_BY_PRODUCT';

insert into SYS_CONFIG
values
	 ('PYD_CHANGE_BY_PRODUCT',
		CAST(N'[' AS nvarchar(max))
		 +N'{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC051910",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC051911",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC051912",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC051913",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC051914",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC101910",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC101911",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC101912",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC101913",changeType:"A"}'
		 +N',{"planCode":"RQ500","migrateCheck":false, "schmCode":"RC101914",changeType:"A"}'
		 +N',{"planCode":"HV599","migrateCheck":true, changeType:"A"}'
		 +N',{"planCode":"HS599","migrateCheck":true, changeType:"A"}'
		 -- 20230927 --IFP_23_RL05_NBUW_11285 added start
         +N',{"planCode":"HU599","migrateCheck":false, changeType:"A"}'
         -- 20230927 --IFP_23_RL05_NBUW_11285 added end
		 +N',{"planCode":"MS105","migrateCheck":false, changeType:"B"}'
         +N',{"planCode":"MSD01","migrateCheck":false, changeType:"B"}'
         -- 20230725 --IFP_23_RL05_NBUW_7642 start
         +N',{"planCode":"UN501","migrateCheck":false, changeType:"B"}'
         +N',{"planCode":"UJ501","migrateCheck":false, changeType:"B"}'
         --IFP_24_RL02_NBUW_14146_I Start
         +N',{"planCode":"UK501","migrateCheck":false, changeType:"B"}'
         +N',{"planCode":"UK505","migrateCheck":false, changeType:"A"}'
         +N',{"planCode":"UK510","migrateCheck":false, changeType:"A"}'
         --IFP_24_RL02_NBUW_14146_I end
         -- 20230725 --IFP_23_RL05_NBUW_7642 end
         -- 20240712 IFP_24_RL04_NBUW_23637 added start
         +N',{"planCode":"MT110","migrateCheck":false, changeType:"B"}'
         +N',{"planCode":"MT115","migrateCheck":false, changeType:"B"}'
         +N',{"planCode":"MT120","migrateCheck":false, changeType:"B"}'
         -- 20240712 IFP_24_RL04_NBUW_23637 added end
      +N']',
		'23-JUL-2023',
		'SYSTEM',
		'08-Sep-2024');


commit;