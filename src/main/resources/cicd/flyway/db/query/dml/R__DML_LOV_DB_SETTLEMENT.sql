------------------------------------------------------------------------------------------
-- Mod. Date : 08 Nov 2022
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_23_RL01_NBUW_2450
-- Mod. Desc : Data Update - Coverage (non-ILAS) (Plan Options)
-------------------------------------------------------------------------------------------
-- Mod. Date : 29 Feb 2024
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_24_RL02_NBUW_14110_BE_B
-- Mod. Desc : 2024 New Product - Genesis (NBUWS)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'DB_SETTLEMENT';

--IFP_24_RL02_NBUW_14110_BE_B start
--insert into list_of_value values('DB_SETTLEMENT',N'[{"code":"C","engDesc":"Instalment","chinDesc":"","attributes":[{}]}]','01-Jan-2023',NULL,'SYSTEM','01-Jan-2023');
insert into list_of_value values(
    'DB_SETTLEMENT',
    CAST(N'[' AS nvarchar(max))
            +N'{"code": "C","engDesc": "Instalment","chinDesc": "","attributes": [{}]}'
            +N',{"code": "S","engDesc": "Single lump-sum with deferral payment","chinDesc": "","attributes": [{}]}'
    +N']',
    '21-Apr-2024',
    NULL,
    'SYSTEM',
    '21-Apr-2024'
);
--IFP_24_RL02_NBUW_14110_BE_B end

commit;
