-------------------------------------------------------------------------------------------
--  Mod. Date : 16 Sep 2024
--  Mod. By   : (MITDC CD) <PERSON>
--  Mod. ref  : IFP_24_RL05_NBUW_22006
--  Mod. Desc : 2024 Jan Release - [Genesis & WIOP3] - Legacy Planning
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'CHK_LEGACY_REL';

insert into sys_config values(
    'CHK_LEGACY_REL',
    CAST(N'[' AS nvarchar(max))
        +N'{"code": "BHU","chinDesc": "丈夫","desc":"Husband"}'
        +N',{"code": "BWI","chinDesc": "妻子","desc":"Wife"}'
        +N',{"code": "BFA","chinDesc": "父親","desc":"Father"}'
        +N',{"code": "BMO","chinDesc": "母親","desc":"Mother"}'
        +N',{"code": "BSG","chinDesc": "兄弟姊妹","desc":"Sibling"}'
        +N',{"code": "BBR","chinDesc": "兄弟","desc":"Brother"}'
        +N',{"code": "BSI","chinDesc": "姊妹","desc":"Sister"}'
        +N',{"code": "BSO","chinDesc": "兒子","desc":"Son"}'
        +N',{"code": "BDA","chinDesc": "女兒","desc":"Daughter"}'
        +N',{"code": "BSP","chinDesc": "配偶","desc":"Spouse"}'
        +N',{"code": "BCH","chinDesc": "子女","desc":"Child"}'
        +N',{"code": "BGP","chinDesc": "祖父母","desc":"Grandparent"}'
        +N',{"code": "BGF","chinDesc": "祖父","desc":"Grandfather"}'
        +N',{"code": "BGM","chinDesc": "祖母","desc":"Grandmother"}'
        +N',{"code": "BAU","chinDesc": "姑母","desc":"Aunt"}'
        +N',{"code": "BUN","chinDesc": "叔叔","desc":"Uncle"}'
        +N',{"code": "BNI","chinDesc": "侄女","desc":"Niece"}'
        +N',{"code": "BNE","chinDesc": "侄子","desc":"Nephew"}'
        +N',{"code": "BCO","chinDesc": "堂表兄弟姊妹","desc":"Cousin"}'
        +N',{"code": "BGC","chinDesc": "孫子","desc":"Grandchild"}'
        +N',{"code": "BGS","chinDesc": "孫子","desc":"Grandson"}'
        +N',{"code": "BGD","chinDesc": "孫女","desc":"Granddaughter"}'
    +N']',
    '03-Nov-2024',
	'SYSTEM',
	'03-Nov-2024');

commit;