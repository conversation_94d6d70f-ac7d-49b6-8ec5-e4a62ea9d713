------------------------------------------------------------------------------------------
-- Mod. Date : 29 Sep 2022
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_22_RL05_NBUW_3653
-- Mod. Desc : COVERAGE OCC CLASS
-------------------------------------------------------------------------------------------
-- Mod. Date : 03 Mar 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_6958
-- Mod. Desc : Data Capture & Update - Coverage Information - Part 2 of 2
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'COVERAGE_OCC_CLASS';

--20230303 IFP_23_RL02_NBUW_6958 modified start
--insert into list_of_value values(
--    'COVERAGE_OCC_CLASS',
--    N'[
--        {"code":"0","engDesc":"0","chinDesc":"","attributes":[{"nbwbCode":"0","eposCode":"0"}]},
--		{"code":"1","engDesc":"1","chinDesc":"","attributes":[{"nbwbCode":"1","eposCode":"1"}]},
--		{"code":"2","engDesc":"2","chinDesc":"","attributes":[{"nbwbCode":"2","eposCode":"2"}]},
--		{"code":"3","engDesc":"3","chinDesc":"","attributes":[{"nbwbCode":"3","eposCode":"3"}]},
--		{"code":"4","engDesc":"4","chinDesc":"","attributes":[{"nbwbCode":"4","eposCode":"4"}]}
--    ]',
--    '06-NOV-2022',
--    NULL,
--    'SYSTEM',
--    '06-NOV-2022'
--);

insert into LIST_OF_VALUE values(
    'COVERAGE_OCC_CLASS',
    CAST(N'[{"code":"1","engDesc":"1","chinDesc":"","attributes":[{"nbwbCode":"1","eposCode":"1"}]}'AS nvarchar(max))
    +N',{"code":"2","engDesc":"2","chinDesc":"","attributes":[{"nbwbCode":"2","eposCode":"2"}]}'
    +N',{"code":"3","engDesc":"3","chinDesc":"","attributes":[{"nbwbCode":"3","eposCode":"3"}]}'
    +N',{"code":"4","engDesc":"4","chinDesc":"","attributes":[{"nbwbCode":"4","eposCode":"4"}]}'
    +N']'
    ,'23-APR-2023'
    ,NULL
    ,'SYSTEM'
    ,'23-APR-2023'
);
--20230303 IFP_23_RL02_NBUW_6958 modified end

commit;
