------------------------------------------------------------------------------------------
-- Mod. Date : 27 Dec 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_2349
-- Mod. Desc : Data Update - Basic Info - Family Benefit, Compassionate Premium Waiver
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'MANULOVE_CARE_IN_SCOPE_PRODUCT';

insert into list_of_value values(
    'MANULOVE_CARE_IN_SCOPE_PRODUCT',
    CAST(N'[{"code":"CY510","engDesc":"ManuLove Care, PAID UP IN 10 YEARS","chinDesc":"","attributes":[{}]}' AS nvarchar(max))
      +N',{"code":"CY520","engDesc":"ManuLove Care, PAID UP IN 20 YEARS","chinDesc":"","attributes":[{}]}'
      +N',{"code":"CY525","engDesc":"ManuLove Care, PAID UP IN 25 YEARS","chinDesc":"","attributes":[{}]}'
      +N',{"code":"CY565","engDesc":"ManuLove Care, PAID UP AT AGE 65","chinDesc":"","attributes":[{}]}'
      +N']',
     '23-APR-2023',
     NULL,
     'SYSTEM',
     '23-APR-2023'
);

commit;
