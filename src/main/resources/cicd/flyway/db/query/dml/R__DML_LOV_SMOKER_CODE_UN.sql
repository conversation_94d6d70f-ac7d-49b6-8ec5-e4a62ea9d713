-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL05_NBUW_7642
-- Mod. Desc : 2023 Nov Release - Data Capture & Update - UL/HNW (Part 2)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'SMOKER_CODE_UN';

insert into list_of_value values(
    'SMOKER_CODE_UN',
    CAST(N'[' AS nvarchar(max))
            +N'{"code": "P","engDesc": "Super preferred non smoker","chinDesc": "","attributes": [{}]}'
            +N',{"code": "O","engDesc": "Preferred non smoker","chinDesc": "","attributes": [{}]}'
            +N',{"code": "Q","engDesc": "Standard plus non smoker","chinDesc": "","attributes": [{}]}'
            +N',{"code": "R","engDesc": "Standard non smoker","chinDesc": "","attributes": [{}]}'
            +N',{"code": "T","engDesc": "Preferred smoker","chinDesc": "","attributes": [{}]}'
            +N',{"code": "U","engDesc": "Standard smoker","chinDesc": "","attributes": [{}]}'
    +N']',
    '05-Nov-2023',
    NULL,
    'SYSTEM',
    '05-Nov-2023'
);

commit;