------------------------------------------------------------------------------------------
-- Mod. Date : 08 May 2024
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_24_RL03_NBUW_20359
-- Mod. Desc : Waiving Address and HKID for existing client
-------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'WAIVE_ID_ADDR_CHNL';


insert into LIST_OF_VALUE values(
    'WAIVE_ID_ADDR_CHNL',
    CAST(N'[' AS nvarchar(max))
        +N'{"code":"E","engDesc":"Waived from ePOS","chinDesc":"","attributes":[{"nbwbCode":"E","eposCode":"E"}]}'
        +N',{"code":"N","engDesc":"","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":"N"}]}'
    +N']',
    '21-Jul-2024',
    NULL,
	'SYSTEM',
	'21-Jul-2024');

commit;

