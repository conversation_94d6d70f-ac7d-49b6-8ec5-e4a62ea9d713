------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 12 Apr 2022
-- Mod. By   : (MITDC CD) Danny Pan
-- Mod. ref  : IFP_22_RL03_NBUW_370
-- Mod. Desc : 2022 May Release - NBUW-370 Support Multi crcy in NB2.0
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'POLICY_CURRENCY';

--insert into list_of_value values('POLICY_CURRENCY',N'[{"code":"04","engDesc":"HKD","chinDesc":"港元","attributes":[{"nbwbCode":"04","eposCode":"04"}]}, {"code":"02","engDesc":"USD","chinDesc":"美元","attributes":[{"nbwbCode":"02","eposCode":"02"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

--IFP_22_RL03_NBUW_370 start
insert into list_of_value values('POLICY_CURRENCY',N'[
 {"code":"04","engDesc":"HKD","chinDesc":"港元","attributes":[{"nbwbCode":"04","eposCode":"04"}]},
 {"code":"02","engDesc":"USD","chinDesc":"美元","attributes":[{"nbwbCode":"02","eposCode":"02"}]},
 {"code":"01","engDesc":"CAD","chinDesc":"加元","attributes":[{"nbwbCode":"01","eposCode":"01"}]},
 {"code":"12","engDesc":"GBP","chinDesc":"英鎊","attributes":[{"nbwbCode":"12","eposCode":"12"}]},
 {"code":"23","engDesc":"AUD","chinDesc":"澳元","attributes":[{"nbwbCode":"23","eposCode":"23"}]},
 {"code":"90","engDesc":"CNY","chinDesc":"人民幣","attributes":[{"nbwbCode":"90","eposCode":"90"}]},
 {"code":"05","engDesc":"SGD","chinDesc":"新加坡元","attributes":[{"nbwbCode":"05","eposCode":"05"}]}
]',
'08-NOV-2020',NULL,'SYSTEM','15-May-2022');
--IFP_22_RL03_NBUW_370 end
commit;
