-------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) <PERSON> Wang
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------
 -- Mod. Date : 24 Oct 2024
 -- Mod. By   : (MITDC CD) Jason Zhang
 -- Mod. ref  : IFP_25_RL01_NBUW_30056
 -- Mod. Desc : Support Quick App in NBUWS Currency
 --------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'ISSUE_AGE_RANGE_FOR_APP_TYPE';

insert into SYS_CONFIG
values
	 ('ISSUE_AGE_RANGE_FOR_APP_TYPE',
		CAST(N'[' AS nvarchar(max))
		    -- 20241025 IFP_25_RL01_NBUW_30056 update start
            --+N'{"planCode":"MT110","schmCode":null,"appType":"S","minAge":"5","maxAge":"50"}'
            --+N',{"planCode":"MT115","schmCode":null,"appType":"S","minAge":"5","maxAge":"50"}'
            --+N',{"planCode":"MT120","schmCode":null,"appType":"S","minAge":"5","maxAge":"50"}'
            +N'{"planCode":"MT110","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            +N',{"planCode":"MT115","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            +N',{"planCode":"MT120","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            +N',{"planCode":"MV510","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            +N',{"planCode":"MV520","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            +N',{"planCode":"MV525","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            +N',{"planCode":"MV530","schmCode":null,"appType":"Q","minAge":"5","maxAge":"50"}'
            -- 20241025 IFP_25_RL01_NBUW_30056 update end
        +N']',
		'08-Sep-2024',
		'SYSTEM',
        '05-Jan-2025');

commit;