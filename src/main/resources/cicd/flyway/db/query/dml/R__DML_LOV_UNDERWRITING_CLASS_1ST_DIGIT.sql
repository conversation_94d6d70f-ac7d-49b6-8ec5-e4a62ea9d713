------------------------------------------------------------------------------------------
-- Mod. Date : 23 Nov 2021
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL01_SB_NB_22894
-- Mod. Desc : [DEV-FE] App Data Summary in NBUWS
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'UNDERWRITING_CLASS_1ST_DIGIT';

insert into list_of_value values('UNDERWRITING_CLASS_1ST_DIGIT',N'[{"code":"1","engDesc":"Medical","chinDesc":"","attributes":[{}]},{"code":"2","engDesc":"Non-Medical","chinDesc":"","attributes":[{}]},{"code":"3","engDesc":"Group Conversion","chinDesc":"","attributes":[{}]},{"code":"4","engDesc":"Term Conversion","chinDesc":"","attributes":[{}]},{"code":"5","engDesc":"Rider Conversion","chinDesc":"","attributes":[{}]},{"code":"6","engDesc":"SMC/ Simp App/ Guar Issue","chinDesc":"","attributes":[{}]},{"code":"7","engDesc":"PRC China Visitor","chinDesc":"","attributes":[{}]},{"code":"8","engDesc":"GIB option exercised","chinDesc":"","attributes":[{}]},{"code":"9","engDesc":"Guaranteed issue (H.O. definition and 206 policies in HK systems)","chinDesc":"","attributes":[{}]},{"code":"R","engDesc":"Random check","chinDesc":"","attributes":[{}]},{"code":"W","engDesc":"Random check is waived & accept original disclosure","chinDesc":"","attributes":[{}]},{"code":"N","engDesc":"Random check & change u/w decision - non-disclosure","chinDesc":"","attributes":[{}]},{"code":"M","engDesc":"Random check & change u/w decision - medical findings","chinDesc":"","attributes":[{}]}]','23-NOV-2021',NULL,'SYSTEM','23-NOV-2021');

commit;
