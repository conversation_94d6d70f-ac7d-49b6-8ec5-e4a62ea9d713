-------------------------------------------------------------------------------------------
 -- Mod. Date : 02 Mar 2023
 -- Mod. By   : (MITDC CD) <PERSON><PERSON><PERSON>
 -- Mod. ref  : IFP_23_RL02_NBUW_6098_3933
 -- Mod. Desc : 2023 Apr Release - Add SIO(Simplified Issue Offer) products for NBUWS valuation rules such as PRD0064-0066
--------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) <PERSON>
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------
 -- Mod. Date : 24 Oct 2024
 -- Mod. By   : (MITDC CD) Jason Zhang
 -- Mod. ref  : IFP_25_RL01_NBUW_30056
 -- Mod. Desc : Support Quick App in NBUWS Currency
 --------------------------------------------------------------------------------------------
 --  Mod. Date : 11 Feb 2025
 --  Mod. By   : (MITDC CD) Tony Wang
 --  Mod. ref  : IFP_25_RL02_NBUW_36709
 --  Mod. Desc : 2025 Apr Release - 2025 New Product - New CI (NBUWS)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'SIO_IN_SCOPE_PRODUCT';

--IFP_23_RL02_NBUW_6098_3933 added start
insert into LIST_OF_VALUE values (
        'SIO_IN_SCOPE_PRODUCT',
        CAST(N'[' AS nvarchar(max))
            +N'{"code":"KH125","engDesc":"ManuPrimo Care (BestStart) - 25","chinDesc":"","attributes":[{}]}'
            +N',{"code":"AL575","engDesc":"Take Care Personal Accident Plan 2","chinDesc":"","attributes":[{}]}'
            +N',{"code":"CW580","engDesc":"Cancer Smart Protection Benefit","chinDesc":"","attributes":[{}]}'
            +N',{"code":"AK575","engDesc":"Take Care Personal Accident Benefit","chinDesc":"","attributes":[{}]}'
            --IFP_25_RL01_NBUW_30056 delete start
            --IFP_24_RL04_NBUW_23637 added start
            --+N',{"code":"MT110","engDesc":"Apollo Protection Linked Plan, Premium Payment Period 10 Years","chinDesc":"","attributes":[{}]}'
            --+N',{"code":"MT115","engDesc":"Apollo Protection Linked Plan, Premium Payment Period 15 Years","chinDesc":"","attributes":[{}]}'
            --+N',{"code":"MT120","engDesc":"Apollo Protection Linked Plan, Premium Payment Period 20 Years","chinDesc":"","attributes":[{}]}'
            --IFP_24_RL04_NBUW_23637 added end
            --IFP_25_RL01_NBUW_30056 delete end
            --IFP_25_RL02_NBUW_36709 added start
            +N',{"code":"KL110","engDesc":"IncomeShield Critical Illness Protector, Paid Up In 10 Years","chinDesc":"","attributes":[{}]}'
            +N',{"code":"KL120","engDesc":"IncomeShield Critical Illness Protector, Paid Up In 20 Years","chinDesc":"","attributes":[{}]}'
            +N',{"code":"KL125","engDesc":"IncomeShield Critical Illness Protector, Paid Up In 25 Years","chinDesc":"","attributes":[{}]}'
            --IFP_25_RL02_NBUW_36709 added end
        +N']',
		'23-Apr-2023',
		NULL,
		'SYSTEM',
		'13-Apr-2025');

--IFP_23_RL02_NBUW_6098_3933 added end

commit;