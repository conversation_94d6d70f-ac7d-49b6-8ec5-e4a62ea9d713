-------------------------------------------------------------------------------------------
-- Mod. Date : 13 Apr 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL05_JMIFUM_328
-- Mod. Desc : 2022 May Release - AKS Migration Development - hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'RECOMMEND_PRODUCT';

insert into SYS_CONFIG values(
    'RECOMMEND_PRODUCT',
    N'[{
      		"seqNo": "1",
      		"objBuying": "3501",
      		"planCode": "3502",
      		"protectPeriodOfRider": "3503",
      		"productSelectedInd": "3504"
      	}, {
      		"seqNo": "2",
      		"objBuying": "3505",
      		"planCode": "3506",
      		"protectPeriodOfRider": "3507",
      		"productSelectedInd": "3508"
      	}, {
      		"seqNo": "3",
      		"objBuying": "3509",
      		"planCode": "3510",
      		"protectPeriodOfRider": "3511",
      		"productSelectedInd": "3512"
      	}, {
      		"seqNo": "4",
      		"objBuying": "3513",
      		"planCode": "3514",
      		"protectPeriodOfRider": "3515",
      		"productSelectedInd": "3516"
      	}, {
      		"seqNo": "5",
      		"objBuying": "3517",
      		"planCode": "3518",
      		"protectPeriodOfRider": "3519",
      		"productSelectedInd": "3520"
      	}, {
      		"seqNo": "6",
      		"objBuying": "3521",
      		"planCode": "3522",
      		"protectPeriodOfRider": "3523",
      		"productSelectedInd": "3524"
      	}, {
      		"seqNo": "7",
      		"objBuying": "3525",
      		"planCode": "3526",
      		"protectPeriodOfRider": "3527",
      		"productSelectedInd": "3528"
      	}, {
      		"seqNo": "8",
      		"objBuying": "3529",
      		"planCode": "3530",
      		"protectPeriodOfRider": "3531",
      		"productSelectedInd": "3532"
      	}, {
      		"seqNo": "9",
      		"objBuying": "3533",
      		"planCode": "3534",
      		"protectPeriodOfRider": "3535",
      		"productSelectedInd": "3536"
      	}, {
      		"seqNo": "10",
      		"objBuying": "3537",
      		"planCode": "3538",
      		"protectPeriodOfRider": "3539",
      		"productSelectedInd": "3540"
      }]',
    NULL,
    'SYSTEM',
    '03-May-2022');

commit;
