------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'DRINKING_HABIT';

insert into list_of_value values('DRINKING_HABIT',N'[{"code":"C","engDesc":"Standard consumption of Chinese Wine per week ","chinDesc":"中國酒","attributes":[{"nbwbCode":"N/A","eposCode":"N/A"}]}, {"code":"R","engDesc":"Standard consumption of Regular Beer per week ","chinDesc":"啤酒","attributes":[{"nbwbCode":"N/A","eposCode":"N/A"}]}, {"code":"S","engDesc":"Standard consumption of Spirits (e.g. Brandy) per week ","chinDesc":"烈酒（例如：白蘭地、伏特加）","attributes":[{"nbwbCode":"N/A","eposCode":"N/A"}]}, {"code":"T","engDesc":"Standard consumption of Table Wine per week ","chinDesc":"餐酒","attributes":[{"nbwbCode":"N/A","eposCode":"N/A"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
