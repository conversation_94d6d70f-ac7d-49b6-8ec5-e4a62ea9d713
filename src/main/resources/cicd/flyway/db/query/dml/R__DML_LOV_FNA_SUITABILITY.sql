-------------------------------------------------------------------------------------------
-- Mod. Date : 13 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_7929
-- Mod. Desc : 2023 Jul Release - Data Capture & Update - RPQ / IFS / AD (for ILAS)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'FNA_SUITABILITY';

insert into list_of_value values(
    'FNA_SUITABILITY',
    N'[
            {"code": "P","engDesc": "Pass","chinDesc": "","attributes": [{}]}
            ,{"code": "F","engDesc": "Fail","chinDesc": "","attributes": [{}]}
            ,{"code": "N","engDesc": "Can''t Tell","chinDesc": "","attributes": [{}]}
    ]',
    '23-Jul-2023',
    NULL,
    'SYSTEM',
    '23-Jul-2023'
);

commit;