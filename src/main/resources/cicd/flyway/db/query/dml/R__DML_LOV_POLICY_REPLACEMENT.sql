------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Sep 2023
-- Mod. By   : Ever<PERSON>t <PERSON>ang
-- Mod. ref  : IFP_23_RL05_NBUW_1612
-- Mod. Desc : Compliance Misc Page
-------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'POLICY_REPLACEMENT';

-- insert into list_of_value values('POLICY_REPLACEMENT',N'[{"code":"Y","engDesc":"Yes","chinDesc":"","attributes":[{"nbwbCode":"Y","eposValue":"Y"}]},{"code":"N","engDesc":"No","chinDesc":"","attributes":[{"nbwbCode":"N","eposValue":"N"}]},{"code":"D","engDesc":"Not yet decided","chinDesc":"","attributes":[{"nbwbCode":"D","eposValue":"D"}]},{"code":"X","engDesc":"N/A","chinDesc":"","attributes":[{"nbwbCode":"X","eposValue":"X"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');
insert into list_of_value values('POLICY_REPLACEMENT',N'[{"code":"Y","engDesc":"Yes","chinDesc":"","attributes":[{"nbwbCode":"Y","eposValue":"Y"}]},{"code":"N","engDesc":"No","chinDesc":"","attributes":[{"nbwbCode":"N","eposValue":"N"}]},{"code":"D","engDesc":"Not yet decided","chinDesc":"","attributes":[{"nbwbCode":"D","eposValue":"D"}]}]','08-NOV-2023',NULL,'SYSTEM','08-NOV-2023');

commit;
