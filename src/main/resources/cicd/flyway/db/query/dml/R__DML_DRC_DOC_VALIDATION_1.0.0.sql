------------------------------------------------------------------------------------------
-- Mod. Date : 02 Sep 2021
-- Mod. By   : Jeffrey <PERSON> 
-- Mod. ref  : IFP_21_RL05_SB_NB_19079
-- Mod. Desc : Document Validation in NBUWS
------------------------------------------------------------------------------------------
-- Mod. Date : 26 Oct 2021
-- Mod. By   : <PERSON> Cheung 
-- Mod. ref  : IFP_22_RL01_SB_NB_22263
-- Mod. Desc : Document Validation in NBUWS (Part II)
------------------------------------------------------------------------------------------
-- Mod. Date : 04 May 2022
-- Mod. By   : (MITDC CD) Auter Zhang
-- Mod. ref  : IFP_22_RL04_NBUW_717
-- Mod. Desc : Support Macau policy in NBUWS (WDL Validation)
------------------------------------------------------------------------------------------
-- Mod. Date : 04 May 2022
-- Mod. By   : (MITDC CD) Renli Niu
-- Mod. ref  : IFP_22_RL04_NBUW_1480
-- Mod. Desc : Change the validation rule of Work Doc List for Multiple ID and OCR Service
------------------------------------------------------------------------------------------
-- Mod. Date : 14 Jul 2022
-- Mod. By   : (MITDC CD) Joan Zhao
-- Mod. ref  : IFP_22_RL04_NBUW_1844
-- Mod. Desc : Add new condition "isHKSCB" for WDL0802/WDL1501/WDL1801/WDL2001
------------------------------------------------------------------------------------------
-- Mod. Date : 05 Aug 2022
-- Mod. By   : (MITDC CD) Everrett Xiang
-- Mod. ref  : IFP_22_RL05_NBUW_1540
-- Mod. Desc : Add New Rule WDL2501 for document IFS-PF
------------------------------------------------------------------------------------------
-- Mod. Date : 08 Aug 2022
-- Mod. By   : (MITDC CD) Ivan Wang
-- Mod. ref  : IFP_22_RL05_NBUW_1785
-- Mod. Desc : Add new rule for beneficiary relationship and policyowner Relationship
-------------------------------------------------------------------------------------------
-- Mod. Date : 14 Sep 2022
-- Mod. By   : (MITDC CD) Daven Wei
-- Mod. ref  : IFP_22_RL05_NBUW_2338
-- Mod. Desc : Add Change Work Doc List validation when I = O
-------------------------------------------------------------------------------------------
-- Mod. Date : 15 Sep 2022
-- Mod. By   : (MITDC CD) Daven Wei
-- Mod. ref  : IFP_22_RL05_NBUW_3556
-- Mod. Desc : Update applicable channel and agent in WDL
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Sep 2022
-- Mod. By   : (MITDC CD) Ives Wang
-- Mod. ref  : IFP_22_RL05_NBUW_2063
-- Mod. Desc : Multiple ID - Support ID for Macau
-------------------------------------------------------------------------------------------
-- Mod. Date : 11 Nov 2022
-- Mod. By   : (MITDC CD) Lane Zeng
-- Mod. ref  : IFP_23_RL01_NBUW_3619
-- Mod. Desc : Multiple ID - Build WDL validation for Macau Entry Proof
-------------------------------------------------------------------------------------------
-- Mod. Date : 07 Nov 2022
-- Mod. By   : (MITDC CD) Allen Liu
-- Mod. ref  : IFP_23_RL01_NBUW_3952
-- Mod. Desc : Multiple ID - further enhancement on support ID
-------------------------------------------------------------------------------------------
-- Mod. Date : 15 Nov 2022
-- Mod. By   : (MITDC CD) Daven Wei
-- Mod. ref  : IFP_23_RL01_NBUW_4566
-- Mod. Desc : Remove WDL0203 for HK agent
-------------------------------------------------------------------------------------------
 -- Mod. Date : 19 Jan 2023
 -- Mod. By   : (MITDC CD) Joe Wang
 -- Mod. ref  : IFP_23_RL02_NBUW_2357
 -- Mod. Desc : 2023 Apr Release - Data Capture - ManuCard
-------------------------------------------------------------------------------------------
-- Mod. Date : 07 Dec 2022
-- Mod. By   : (MITDC CD) Daven Wei
-- Mod. ref  : IFP_23_RL04_NBUW_4741
-- Mod. Desc : Update the WDL validation related to other country supporting document
-------------------------------------------------------------------------------------------
-- Mod. Date : 17 Feb 2023
-- Mod. By   : (MITDC CD) Eric Chen
-- Mod. ref  : IFP_23_RL02_NBUW_2349_UAT_BUG_6881
-- Mod. Desc : Fix issue for WDL0312/WDL0313 is display when MLC document type is HK / Macau ID
-------------------------------------------------------------------------------------------
 -- Mod. Date : 27 Apr 2023
 -- Mod. By   : (MITDC CD) Joan Zhao
 -- Mod. ref  : IFP_23_RL04_NBUW_6862
 -- Mod. Desc : 2023 Apr Release -  Update the WDL validation related to PAPER
-------------------------------------------------------------------------------------------
 -- Mod. Date : 27 Jun 2023
 -- Mod. By   : (MITDC CD) Bruce Hu
 -- Mod. ref  : IFP_23_RL04_NBUW_7176
 -- Mod. Desc : 2023 July Release -  Data Capture & Update - Plan and Coverage (ILAS)
------------------------------------------------------------------------------------------
 -- Mod. Date : 10 Aug 2023
 -- Mod. By   : (MITDC CD) David Yang
 -- Mod. ref  : IFP_23_RL05_NBUW_11288
 -- Mod. Desc : 2023 Nov Release -  Fix WDL0338 validation trigger logic
------------------------------------------------------------------------------------------
-- Mod. Date : 25 Sep 2023
-- Mod. By   : (MITDC CD) Auter Zhang
-- Mod. ref  : IFP_23_RL05_NBUW_11420
-- Mod. Desc : Support MA OCR data in NBUWS
------------------------------------------------------------------------------------------
-- Mod. Date : 24 Jan 2024
-- Mod. By   : (MITDC CD) Renli Niu
-- Mod. ref  : IFP_24_RL03_NBUW_14108_16290
-- Mod. Desc : Add WDL and update WDL validations to support SPX
------------------------------------------------------------------------------------------
-- Mod. Date : 02 Apr 2024
-- Mod. By   : (MITDC CD) Renli Niu
-- Mod. ref  : IFP_24_RL03_NBUW_18173
-- Mod. Desc : Fix WDL0402 for SPX
------------------------------------------------------------------------------------------
-- Mod. Date : 08 May 2024
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_24_RL03_NBUW_20359
-- Mod. Desc : Waiving Address and HKID for existing client
-------------------------------------------------------------------------------------------
DELETE FROM DOC_RULE_CTRL WHERE CTRL_NAME = 'DOC_CTRL';

-- EPOSAPP
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0101','EPOSAPP'
,N'$[?((@.isHKAgency == ''Y'' || @.isMCAgency == ''Y'') && @.eligibelChnl != null )]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"eligibelChnl":"$[?(@.appData.chnlCode == ''E'')].appData.chnlCode"
}'
,'Application Summary not found'
--IFP_23_RL04_NBUW_6862 commented start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
--IFP_23_RL04_NBUW_6862 commented end

-- IFP_22_RL01_SB_NB_22263 start

-- EAPPLFORM
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0102','EAPPLFORM'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.backScanAppDocName != null && @.ePOSDigitAppForm == null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"backScanAppDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''EAPPLFORM'' )].docName",
"ePOSDigitAppForm":"$[0].appData.subDoc.workDocList[?(@.docType == ''EPOSAPP'' )].docName"
}'
,'Backscan Application Summary Found, please follow up in NBWB'
--20240122 IFP_24_RL03_NBUW_14108_16290 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','21-Jul-2024','SYSTEM','21-Jul-2024','SYSTEM',null,null);
--20240122 IFP_24_RL03_NBUW_14108_16290 modified end


-- IFP_22_RL01_SB_NB_22263 end

-- PS
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0201','PS'
,N'$[?(@.eligibelChnl != null && @.convertPolicyNo == null && @.migrationPlanCode == null)]'
,N'{
"eligibelChnl":"$[?(@.appData.chnlCode == null || @.appData.chnlCode== ''E'' || @.appData.chnlCode== ''Q'')].appData.chnlCode",
"convertPolicyNo":"$[0].appData.convertPolicyNo",
"migrationPlanCode":"$[0].appData.coverage.coveragePlanList[?(@.migratePolicyNo != null || @.migratePlanCode != null )].planCode"
}'
,'Proposal Summary not found'
--IFP_23_RL04_NBUW_6862 commented start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
--IFP_23_RL04_NBUW_6862 commented end

-- NONPS
--20220916 IFP_22_RL05_NBUW_3556 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0202','NONPS'
--,N'$[?(@.eligibelChnl != null && ((@.pdfPaymentPolicy != null  || @.anyPolicyRider != null) || @.nonPSForm != null))]'
--,N'{
--"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'' || @.appData.chnlCode== ''Q'')].appData.chnlCode",
--"pdfPaymentPolicy":"$[?(@.appData.paymentMethod==''2'')].appData.policyNo",
--"anyPolicyRider":"$[0].appData.coverage.coveragePlanList[?(@.planCat==''R'')].planCode",
--"nonPSForm":"$[0].appData.subDoc.workDocList[?(@.docType == ''NONPS'' )].docName"
--}'
--,'Full Proposal without the PS page not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0202','NONPS'
,N'$[?((@.eligibelChnl != null || @.noPaperChnl == null) && (@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' ) && (@.pdfPaymentPolicy != null  || @.anyPolicyRider != null)|| @.nonPSForm != null)]'
,N'{
"eligibelChnl":"$[?(@.appData.chnlCode== ''E'' || @.appData.chnlCode== ''Q'')].appData.chnlCode",
"noPaperChnl":"$[?(@.appData.chnlCode != null)].appData.chnlCode",
"pdfPaymentPolicy":"$[?(@.appData.paymentMethod==''2'')].appData.policyNo",
"anyPolicyRider":"$[0].appData.coverage.coveragePlanList[?(@.planCat==''R'')].planCode",
"nonPSForm":"$[0].appData.subDoc.workDocList[?(@.docType == ''NONPS'' )].docName",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB"
}'
,'Full Proposal without the PS page not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220916 IFP_22_RL05_NBUW_3556 added end

-- ID_INS
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0301','ID_INS'
,N'$[?(@.policyNo != null)]'
,N'{
"policyNo":"$[0].appData.policyNo"
}'
,'Basic Insured ID Copy / Birth certificate not found'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ID_OWN
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0302','ID_OWN'
,N'$[?(@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)]'
,N'{
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Policyowner ID not found'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ID_PYR
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0303','ID_PYR'
,N'$[?(@.payorClientId != null && @.ownerClientId != @.payorClientId)]'
,N'{
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId"
}'
,'Payor ID not found'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- IFP_22_RL01_SB_NB_22263 start

--20220818 IFP_22_RL05_NBUW_2113 added start
-- ID_MPG
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0304','ID_MPG'
--,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.isEmptyOrDDAIdDocName != null)]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
--"isEmptyOrDDAIdDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && (@.role == '''' || @.role == ''DDA'') )].docName"
--}'
--,'ID with thirty party cannot be define, please follow up in NBWB'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0304','ID_MPG'
--,N'$[?(@.insuredAge < 18 || @.relationProof != null)]'
--,N'{
--"relationProof":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''MPG'' )].docName",
--"insuredAge":"$[0].appData.extParams.insuredAge"
--}'
--,'Relationship proof for juvenile case not found'
--,'Y','01-Nov-2022','SYSTEM','01-Nov-2022','SYSTEM',null,null);
--20220818 IFP_22_RL05_NBUW_2113 added end
--20220916 IFP_22_RL05_NBUW_3556 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0304','ID_MPG'
--,N'$[?((@.insuredAge < 18 && (@.legalIDType != ''2'' && @.legalIDType != ''3'' && @.legalIDType != ''4'') && (@.isHKAgency == ''Y'' || @.isMCAgency == ''Y'')) || @.relationProof != null)]'
--,N'{
--"relationProof":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''MPG'' )].docName",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"insuredAge":"$[0].appData.extParams.insuredAge",
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"isMCAgency":"$[0].appData.extParams.isMCAgency"
--}'
--,'Relationship proof for juvenile case not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0304','ID_MPG'
,N'$[?((@.insuredAge < 18 && (@.legalIDType != ''2'' && @.legalIDType != ''3'' && @.legalIDType != ''4'')) || @.relationProof != null)]'
,N'{
"relationProof":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''MPG'' )].docName",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"insuredAge":"$[0].appData.extParams.insuredAge"
}'
,'Relationship proof for juvenile case not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220916 IFP_22_RL05_NBUW_3556 added end

-- ID_TRD
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0305','ID_TRD'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.isEmptyOrDDAIdDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
"isEmptyOrDDAIdDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && (@.role == '''' || @.role == ''DDA'') )].docName"
}'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'ID with thirty party cannot be define, please follow up in NBWB'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'ID with third party cannot be define, please follow up in NBWB'
,'Y','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end


-- ID_DAD
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0306','ID_DAD'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.isEmptyOrDDAIdDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
"isEmptyOrDDAIdDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && (@.role == '''' || @.role == ''DDA'') )].docName"
}'
,'ID with thirty party cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ID_
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0307','ID_*'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.isEmptyOrDDAIdDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
"isEmptyOrDDAIdDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && (@.role == '''' || @.role == ''DDA'') )].docName"
}'
,'ID with thirty party cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

--20220705 IFP_22_RL04_NBUW_1480 modified start
-- ID_
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0308','ID_*'
--,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.isEmptyOrDDAIdDocName != null)]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
--"isEmptyOrDDAIdDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && (@.role == '''' || @.role == ''DDA'') )].docName"
--}'
--,'ID with thirty party cannot be define, please follow up in NBWB'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0308','ID,PASSPORT_OWN___NOTBIRTH_LEG'
,N'$[?(((@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.id != null || @.passport != null )]'
,N'{
"id":"$[0].appData.subDoc.workDocList[?(@.role== ''OWN'' && @.docType == ''ID'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
"passport":"$[0].appData.subDoc.workDocList[?(@.role== ''OWN'' && @.docType == ''PASSPORT'' && @.idType == ''LEG''&& (@.documentType != ''BIRTH''))]",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Policyowner''s Legal ID/PASSPORT not found'
--IFP_24_RL03_NBUW_20359 start
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
,'N','21-Jul-2024','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--IFP_24_RL03_NBUW_20359 end
--20220705 IFP_22_RL04_NBUW_1480 modified end


-- IFP_22_RL01_SB_NB_22263 end

-- EDDA
--20220705 IFP_22_RL04_NBUW_1480 modified start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0601','EDDA'
--,N'$[?( (@.isHKAgency == ''Y'') && @.eligibelChnl != null && (@.paymentMode == ''1'' || @.paymentMethod == ''4''))]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
--"paymentMethod":"$[0].appData.paymentMethod",
--"paymentMode":"$[0].appData.paymentMode"
--}'
--,'Direct Debit Authorization Form (DDA) not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
--20220916 IFP_22_RL05_NBUW_3556 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0601','EDDA,MCBOCDDA'
--,N'$[?( (@.isHKAgency == ''Y'' || @.isMCAgency == ''Y'') && @.eligibelChnl != null && (@.paymentMode == ''1'' || @.paymentMethod == ''4''))]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"isMCAgency":"$[0].appData.extParams.isMCAgency",
--"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
--"paymentMethod":"$[0].appData.paymentMethod",
--"paymentMode":"$[0].appData.paymentMode"
--}'
--,'Direct Debit Authorization Form (DDA) not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0601','EDDA,SDDA,DDA,BOCDDA,BOCDDAU,DBSDDAU,EBOCDDAU,EDBSDDAU'
,N'$[?(((@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' ) && (@.eligibelChnl != null || @.noPaperChnl == null) && (@.paymentMode == ''1'' || @.paymentMethod == ''4'')) || @.docName != null)]'
,N'{
"docName":"$[0].appData.subDoc.workDocList[?(@.docType == ''EDDA'' || @.docType == ''SDDA'' || @.docType == ''DDA'' || @.docType == ''BOCDDA'' || @.docType == ''BOCDDAU'' || @.docType == ''DBSDDAU'' || @.docType == ''EBOCDDAU'' || @.docType == ''EDBSDDAU'')].docName",
"eligibelChnl":"$[?(@.appData.chnlCode== ''E'' || @.appData.chnlCode== ''Q'')].appData.chnlCode",
"noPaperChnl":"$[?(@.appData.chnlCode != null)].appData.chnlCode",
"paymentMethod":"$[0].appData.paymentMethod",
"paymentMode":"$[0].appData.paymentMode",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB"
}'
,'Direct Debit Authorization Form (DDA) not found'
-- 20230119 IFP_23_RL02_NBUW_2357 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
-- 20230119 IFP_23_RL02_NBUW_2357 modified end

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0602','MCBOCDDA,MCDDA'
,N'$[?(((@.isMCAgency == ''Y'' || @.isMCBroker == ''Y'') && (@.eligibelChnl != null || @.noPaperChnl == null) && (@.paymentMode == ''1'' || @.paymentMethod == ''4'')) || @.docName != null)]'
,N'{
"docName":"$[0].appData.subDoc.workDocList[?(@.docType == ''MCBOCDDA'' || @.docType == ''MCDDA'')].docName",
"eligibelChnl":"$[?(@.appData.chnlCode== ''E'')].appData.chnlCode",
"noPaperChnl":"$[?(@.appData.chnlCode != null)].appData.chnlCode",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"isMCBroker":"$[0].appData.extParams.isMCBroker",
"paymentMethod":"$[0].appData.paymentMethod",
"paymentMode":"$[0].appData.paymentMode"
}'
,'Direct Debit Authorization Form (DDA) not found'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220916 IFP_22_RL05_NBUW_3556 added end
-- IFSPR
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0802','IFSPR'
,N'$[?( (@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' ) && @.eligibelChnl != null && (@.polReplaceCode == ''Y'' || @.polReplaceCode == ''D''))]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'' || @.appData.chnlCode== ''Q'')].appData.chnlCode",
"polReplaceCode":"$[0].appData.polReplaceCode"
}'
,'Important Facts Statement - Policy Replacement (IFS-PR) not found'
,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);

-- ADDRESS
--20220705 IFP_22_RL04_NBUW_1480 modified start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1501','ADDRESS,AGTDECL'
--,N'$[?( ((@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y'') && @.eligibelChnl != null) || @.addrProof != null || @.agtDecl != null)]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"isHKBroker":"$[0].appData.extParams.isHKBroker",
--"isHKDBS":"$[0].appData.extParams.isHKDBS",
--"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
--"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
--"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
--"addrProof":"$[0].appData.subDoc.workDocList[?(@.docType == ''ADDRESS'' )].docName",
--"agtDecl":"$[0].appData.subDoc.workDocList[?(@.docType == ''AGTDECL'' )].docName"
--}'
--,'Policyowner Address Proof not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1501','ADDRESS,AGTDECL'
,N'$[?( ((@.isHKAgency == ''Y'' || @.isMCAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'') && @.eligibelChnl != null) || @.addrProof != null || @.agtDecl != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"addrProof":"$[0].appData.subDoc.workDocList[?(@.docType == ''ADDRESS'' )].docName",
"agtDecl":"$[0].appData.subDoc.workDocList[?(@.docType == ''AGTDECL'' )].docName"
}'
,'Policyowner Address Proof not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);--20240125 IFP_24_RL03_NBUW_14108_16290 commented
,'N','21-Jul-2024','SYSTEM','21-Jul-2024','SYSTEM',null,null);--20240125 IFP_24_RL03_NBUW_14108_16290 modified
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- MCV001
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1601','MCV001'
,N'$[?( @.isHKAgency == ''Y'' && @.eligibelChnl != null && @.isMCVResident == ''Y'')]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"isMCVResident":"$[0].appData.extParams.docValidationParam.isMCVResident"
}'
,'MCV  - Form, Travel Doc & Entry Permit not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);--20240125 IFP_24_RL03_NBUW_14108_16290 commented
,'N','21-Jul-2024','SYSTEM','21-Jul-2024','SYSTEM',null,null);--20240125 IFP_24_RL03_NBUW_14108_16290 modified

-- IFSMP
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1602','IFSMP'
,N'$[?(@.eligibelChnl != null && @.isMCVResident == ''Y'')]'
,N'{
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"isMCVResident":"$[0].appData.extParams.docValidationParam.isMCVResident"
}'
,'Important Facts Statement for Mainland Policyholder not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);--20240130 IFP_24_RL03_NBUW_14108_16290 commented
,'N','21-Jul-2024','SYSTEM','21-Jul-2024','SYSTEM',null,null);--20240130 IFP_24_RL03_NBUW_14108_16290 modified

-- IFP_22_RL01_SB_NB_22263 start

-- ENTRY_OWN_Z04
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1603','ENTRY_OWN_Z04'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.entryDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' )].docName"
}'
,'ENTRY cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ENTRY_INS_Z04
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1604','ENTRY_INS_Z04'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.entryDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' )].docName"
}'
,'ENTRY cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ENTRY_PYR_Z03
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1605','ENTRY_PYR_Z03'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.entryDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' )].docName"
}'
,'ENTRY cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ENTRY_INS_Z03
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1606','ENTRY_INS_Z03'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.entryDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' )].docName"
}'
,'ENTRY cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- ENTRY_OWN_Z03
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1607','ENTRY_OWN_Z03'
,N'$[?(@.isHKAgency == ''Y'' && @.eligibelChnl != null && @.entryDocName != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' )].docName"
}'
,'ENTRY cannot be define, please follow up in NBWB'
--20220705 IFP_22_RL04_NBUW_1480 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','02-Jan-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220705 IFP_22_RL04_NBUW_1480 modified end

-- IFP_22_RL01_SB_NB_22263 end

-- MLCSUPP
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1801','MLCSUPP'
,N'$[?( (@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isMCAgency == ''Y''|| @.isHKSCB == ''Y'' ) && @.eligibelChnl != null && (@.planCode == ''CY510'' || @.planCode == ''CY520'' || @.planCode == ''CY525'' || @.planCode == ''CY565''))]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"planCode":"$[0].appData.planCode"
}'
,'ManuLove Care supplementary form not found'
-- 20240207 IFP_24_RL03_NBUW_14108_16290 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','21-Jul-2024','SYSTEM','21-Jul-2024','SYSTEM',null,null);
--20240207 IFP_24_RL03_NBUW_14108_16290 modified end


-- AGTSTAT
--20220705 IFP_22_RL04_NBUW_1480 modified start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL2001','AGTSTAT'
--,N'$[?(((@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y'' ) && @.eligibelChnl != null) || @.AgtStateDoc != null)]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"isHKBroker":"$[0].appData.extParams.isHKBroker",
--"isHKDBS":"$[0].appData.extParams.isHKDBS",
--"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
--"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
--"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
--"AgtStateDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''AGTSTAT'' )].docName"
--}'
--,'Advisor statement (AD) not found'
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL2001','AGTSTAT'
,N'$[?(((@.isHKAgency == ''Y'' || @.isMCAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' ) && @.eligibelChnl != null) || @.AgtStateDoc != null)]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"AgtStateDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''AGTSTAT'' )].docName"
}'
,'Advisor statement (AD) not found'
-- 20240122 IFP_24_RL03_NBUW_14108_16290 modified start
--,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
,'N','21-Jul-2024','SYSTEM','21-Jul-2024','SYSTEM',null,null);
--20240122 IFP_24_RL03_NBUW_14108_16290 modified end
--20220705 IFP_22_RL04_NBUW_1480 modified end

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL2301','BENEL'
,N'$[?(@.isMCAgency == ''Y'')]'
,N'{
"isMCAgency":"$[0].appData.extParams.isMCAgency"
}'
,'Benefit Limit not found'
,'Y','14-Jan-2022','SYSTEM','14-Jan-2022','SYSTEM',null,null);


INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0203','NONPS'
,N'$[?(!((@.isMCAgency == ''Y'') && (@.planCode == ''HT599'' || @.planCode == ''AL575'')) || (@.isHKAgency == ''Y'' && @.nonPSForm != null))]'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"nonPSForm":"$[0].appData.subDoc.workDocList[?(@.docType == ''NONPS'' )]",
"planCode":"$[0].appData.planCode"
}'
,'Full Proposal without the PS page not found'
--,'Y','14-Jan-2022','SYSTEM','14-Jan-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','14-Jan-2022','SYSTEM','14-Jan-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1202','CPD'
,N'$[?(@.isMCAgency == ''Y'' && (@.policyReplacement == ''Y'' || (@.policyReplacement == ''N'' && @.cpd != null)))]'
,N'{
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"policyReplacement":"$[0].appData.polReplaceCode",
"cpd":"$[0].appData.subDoc.workDocList[?(@.docType == ''CPD'' )]"
}'
,'Macau Policy Replacement is ''Y'' but CPD form not found.'
,'Y','07-Feb-2022','SYSTEM','07-Feb-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1203','CPD'
,N'$[?(@.isMCAgency == ''Y'' && @.policyReplacement != ''N'' && @.cpd != null)]'
,N'{
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"policyReplacement":"$[0].appData.polReplaceCode",
"cpd":"$[0].appData.subDoc.workDocList[?(@.docType == ''CPD'' )]"
}'
,'Macau Policy Replacement is ''N'' but CPD form is submitted. Please check the answer of Policy Replacement'
,'Y','07-Feb-2022','SYSTEM','07-Feb-2022','SYSTEM',null,null);

-- 20220504 IFP_22_RL04_NBUW_717 added start
INSERT into DOC_RULE_CTRL values (
    'DOC_CTRL'
    ,'WDL0402'
    ,'FNA'
    -- for compatibility, for HK Policy must keep condition same with java code(that is mean keep @.fna != null same with isDocExist).
    ,N'$[?(@.isMCPolicy == ''Y'' || (@.isHKPolicy == ''Y'' && @.fna != null))]'
    -- the below condition is real condition(it is take in workDocList is null or empty)
    --,N'$[?(@.isMCPolicy == ''Y'' && (@.fna == null || @.fna.size == 0))]'
    --IFP_23_RL04_NBUW_7176 start
--    ,N'{
--        "isMCPolicy": "$[0].appData.extParams.isMCPolicy"
--        ,"isHKPolicy": "$[0].appData.extParams.isHKPolicy"
--        ,"fna": "$[0].appData.subDoc.workDocList[?(@.docType == ''FNA'')]"
--    }'
    ,N'{
        "isMCPolicy": "$[0].appData.extParams.isMCAgency"
        ,"isHKPolicy": "$[0].appData.extParams.isHKAgency"
        ,"fna": "$[0].appData.subDoc.workDocList[?(@.docType == ''FNA'')]"
    }'
    --IFP_23_RL04_NBUW_7176 end
    ,'Financial Needs Analysis - MA  not found'
    --240402 IFP_24_RL03_NBUW_18173 modified start
    --,'Y'
    ,'N'
    --240402 IFP_24_RL03_NBUW_18173 modified end
    ,'07-Jul-2022'
    ,'SYSTEM'
    ,'07-Jul-2022'
    ,'SYSTEM'
    ,null
    ,null
);

-- 20220504 IFP_22_RL04_NBUW_717 added end

--20220705 IFP_22_RL04_NBUW_1480 added start
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0103','REINFORM'
,N'$[?((@.appliedPayorBenefit !=null && @.eligibelChnl == null) || @.reinFormDocName != null)]'
,N'{
"reinFormDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''REINFORM'')].docName",
"eligibelChnl":"$[0].appData.chnlCode",
"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]"
}'
,'Application for Payor Benefit (U36) not found'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0309','ID,PASSPORT_INS___NOTBIRTH_LEG'
--,N'$[?(((@.insuredAge >= 11 && @.declaredNationality == ''HK'') || (@.insuredAge >= 5 && @.declaredNationality == ''MO'') || (@.insuredAge >= 18 && (@.declaredNationality != ''HK''  || @.declaredNationality != ''MO''))) || @.id != null || @.passport != null)]'
--,N'{
--"id":"$[0].appData.subDoc.workDocList[?(@.role== ''INS'' && @.docType == ''ID'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
--"passport":"$[0].appData.subDoc.workDocList[?(@.role== ''INS'' && @.docType == ''PASSPORT'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"insuredAge":"$[0].appData.extParams.insuredAge"
--}'
--,'Insured''s Legal ID/PASSPORT not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0309','ID,PASSPORT_INS___NOTBIRTH_LEG'
,N'$[?((((@.insuredAge >= 11 && @.declaredNationality == ''HK'') || (@.insuredAge >= 5 && @.declaredNationality == ''MO'') || (@.insuredAge >= 18 && (@.declaredNationality != ''HK''  || @.declaredNationality != ''MO'')))&&(@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.id != null || @.passport != null)]'
,N'{
"id":"$[0].appData.subDoc.workDocList[?(@.role== ''INS'' && @.docType == ''ID'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
"passport":"$[0].appData.subDoc.workDocList[?(@.role== ''INS'' && @.docType == ''PASSPORT'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId",
"insuredAge":"$[0].appData.extParams.insuredAge"
}'
,'Insured''s Legal ID/PASSPORT not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220826 IFP_22_RL05_NBUW_2338 added end
--20220818 IFP_22_RL05_NBUW_2113 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0310','ID_INS___BIRTH_LEG'
--,N'$[?((@.insuredAge < 11 && @.declaredNationality == ''HK'') || (@.insuredAge < 5 && @.declaredNationality == ''MO'') || (@.insuredAge < 18 && (@.declaredNationality != ''HK'' && @.declaredNationality != ''MO'')) || @.birthCertificate != null)]'
--,N'{
--"birthCertificate":"$[0].appData.subDoc.workDocList[?(@.role== ''INS'' && @.docType == ''ID'' && @.documentType == ''BIRTH'' && @.idType == ''LEG'')]",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"insuredAge":"$[0].appData.extParams.insuredAge"
--}'
--,'Insured''s Birth Certificate not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0310','ID_INS____LEG'
,N'$[?((@.insuredAge < 11 && @.declaredNationality == ''HK'') || (@.insuredAge < 5 && @.declaredNationality == ''MO'') || (@.insuredAge < 18 && (@.declaredNationality != ''HK'' && @.declaredNationality != ''MO'')) || @.id != null)]'
,N'{
"id":"$[0].appData.subDoc.workDocList[?(@.role== ''INS'' && @.docType == ''ID'' && @.idType == ''LEG'')]",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"insuredAge":"$[0].appData.extParams.insuredAge"
}'
,'Insured''s Birth Certificate/Legal ID not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220818 IFP_22_RL05_NBUW_2113 added end

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0311','ID,PASSPORT_PYR___NOTBIRTH_LEG'
,N'$[?((@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId) || @.id !=null || @.passport !=null)]'
,N'{
"id":"$[0].appData.subDoc.workDocList[?(@.role== ''PYR'' && @.docType == ''ID'' && @.idType == ''LEG''&& (@.documentType != ''BIRTH''))]",
"passport":"$[0].appData.subDoc.workDocList[?(@.role== ''PYR'' && @.docType == ''PASSPORT'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId"
}'
,'Payor''s Legal ID/PASSPORT not found'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0312','ID_DAD'
,N'$[?((@.planCode == ''CY510'' || @.planCode == ''CY520'' || @.planCode == ''CY525'' || @.planCode == ''CY565'') && @.fatherInFamilyBenefit != null && ((@.issuedCountry != ''HK'' && (@.documentType != ''PREM'' || @.documentType != ''NON-PREM'')) && (@.issuedCountry != ''MO'')) || @.id != null)]'
,N'{
"planCode":"$[0].appData.planCode",
"fatherInFamilyBenefit":"$[0].appData.familyMemberInfo.familyMemberList[?(@.relationship==''FFA'')]",
"id":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''DAD'' )].docName",
"issueCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''DAD'' )].issuedCountry",
"documentType":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''DAD'' )].documentType"
}'
,'DAD''s non-HK/ Macau ID not found for MLC family benefit application.'
--20230217 IFP_23_RL02_NBUW_2349_UAT_BUG_6881 modified start
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null);
--20230217 IFP_23_RL02_NBUW_2349_UAT_BUG_6881 modified end

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0313','ID_MUM'
,N'$[?((@.planCode == ''CY510'' || @.planCode == ''CY520'' || @.planCode == ''CY525'' || @.planCode == ''CY565'') && @.motherInFamilyBenefit != null && ((@.issuedCountry != ''HK'' && (@.documentType != ''PREM'' || @.documentType != ''NON-PREM'')) && (@.issuedCountry != ''MO'')) || @.id != null)]'
,N'{
"planCode":"$[0].appData.planCode",
"motherInFamilyBenefit":"$[0].appData.familyMemberInfo.familyMemberList[?(@.relationship==''FMO'')]",
"id":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''MUM'' )].docName",
"issueCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''MUM'' )].issuedCountry",
"documentType":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''MUM'' )].documentType"
}'
,'MUM''s non-HK/ Macau ID not found  for MLC family benefit application.'
--20230217 IFP_23_RL02_NBUW_2349_UAT_BUG_6881 modified start
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null);
--20230217 IFP_23_RL02_NBUW_2349_UAT_BUG_6881 modified end
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0314','PASSPORT,ENTRY_OWN__HK__SUP'
--,N'$[?((((@.legalIDIssuedCountry == ''HK'' && @.declaredNationality == ''HK'' && @.legalIDType == ''3'') || (@.legalIDIssuedCountry != ''HK'' && @.declaredNationality == ''HK'')) && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.hkReentryPermit != null || @.hkPassport != null)]'
--,N'{
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"hkReentryPermit":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.documentType == ''RE'' && @.role == ''OWN'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''OWN'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
--"hkPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s nationality proof not found. (Declared Nationality = HK)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0314','PASSPORT,ENTRY_OWN__HK__SUP'
,N'$[?((( @.declaredNationality == ''HK'' && @.legalIDType == ''3'') || (@.legalIDIssuedCountry != ''HK'' && @.declaredNationality == ''HK'')) || @.hkReentryPermit != null || @.hkPassport != null)]'
,N'{
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
"hkReentryPermit":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.documentType == ''RE'' && @.role == ''OWN'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''OWN'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"hkPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]"
}'
,'Policyowner''s nationality proof not found. (Declared Nationality = HK)'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0315','PASSPORT,ENTRY_INS__HK__SUP'
--,N'$[?(((@.declaredNationality ==''HK'' && @.legalIDType == ''3'' && @.insuredAge >= 18) || (@.legalIDIssuedCountry != ''HK'' && @.declaredNationality ==''HK'')) || @.hkReentryPermit != null || @.hkPassport != null || @.travelDoc != null)]'
--,N'{
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"insuredAge":"$[0].appData.extParams.insuredAge",
--"hkPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''INS'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
--"hkReentryPermit":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.documentType == ''RE'' && @.role == ''INS'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]"
--}'
--,'Insured''s nationality proof not found. (Declared Nationality = HK)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0315','PASSPORT,ENTRY_INS__HK__SUP'
,N'$[?((((@.declaredNationality ==''HK'' && @.legalIDType == ''3'' && @.insuredAge >= 18) || (@.legalIDIssuedCountry != ''HK'' && @.declaredNationality ==''HK'')) && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.hkReentryPermit != null || @.hkPassport != null || @.travelDoc != null)]'
,N'{
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"insuredAge":"$[0].appData.extParams.insuredAge",
"hkPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''INS'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"hkReentryPermit":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.documentType == ''RE'' && @.role == ''INS'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s nationality proof not found. (Declared Nationality = HK)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220826 IFP_22_RL05_NBUW_2338 added end
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0316','PASSPORT,ENTRY_PYR__HK__SUP'
,N'$[?(((@.appliedPayorBenefit != null && @.declaredNationality == ''HK'' && @.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId) && (@.legalIDType == ''3'' || @.legalIDIssuedCountry != ''HK'')) || @.hkReentryPermit != null || @.hkPassport != null || @.travelDoc!= null)]'
,N'{
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"hkPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''PYR'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]",
"hkReentryPermit":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.documentType == ''RE'' && @.role == ''PYR'' && @.issuedCountry == ''HK'' && @.idType == ''SUP'')]"
}'
,'Payor''s nationality proof not found. (Declared Nationality = HK)'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added start
--20220830 IFP_22_RL05_NBUW_2063 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0317','ID,PASSPORT_OWN__MO__SUP'
--,N'$[?(((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId))) || @.macauId != null || @.macauPassport != null || @.travelDoc != null)]'
--,N'{
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0317','ID,PASSPORT_OWN__MO__SUP'
--,N'$[?(((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'')) || @.macauId != null || @.macauPassport != null || @.travelDoc != null)]'
--,N'{
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
--}'
--,'Policyowner''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0318','ID,PASSPORT_INS__MO__SUP'
--,N'$[?((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'') || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null)]'
--,N'{
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
--}'
--,'Insured''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0318','ID,PASSPORT_INS__MO__SUP'
--,N'$[?((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null)]'
--,N'{
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Insured''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0319','ID,PASSPORT_PYR__MO__SUP'
--,N'$[?((@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId && @.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' && @.appliedPayorBenefit != null) || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null)]'
--,N'{
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
--"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]",
--"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
--}'
--,'Payor''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--IFP_23_RL01_NBUW_3952 modified start
--WDL0317
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0317','ID,PASSPORT_OWN__MO__'
--,N'$[?((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' )|| @.macauId != null || @.macauPassport != null || @.travelDoc != null )]'
--,N'{
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' )]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
--}'
--,'Policyowner''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--
----WDL0318
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0318','ID,PASSPORT_INS__MO__'
--,N'$[?((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO''&& (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null )]'
--,N'{
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role==''INS'' && @.issuedCountry == ''MO'' )]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Insured''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--
----WDL0319
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0319','ID,PASSPORT_PYR__MO__'
--,N'$[?((@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId && @.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' && @.appliedPayorBenefit != null) || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null )]'
--,N'{
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
--"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]",
--"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry",
--"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' )]",
--"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
--"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
--}'
--,'Payor''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0317','ID,PASSPORT_OWN__MO__SUP'
,N'$[?((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'') || @.macauId != null || @.macauPassport != null || @.travelDoc != null)]'
,N'{
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
}'
,'Policyowner''s nationality proof not found. (Declared Nationality = Macau)'
,'N','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0318','ID,PASSPORT_INS__MO__SUP'
,N'$[?((@.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null)]'
,N'{
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role==''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s nationality proof not found. (Declared Nationality = Macau)'
,'N','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0319','ID,PASSPORT_PYR__MO__SUP'
,N'$[?((@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId && @.legalIDIssuedCountry != ''MO'' && @.declaredNationality==''MO'' && @.appliedPayorBenefit != null) || @.macauId != null || @.macauPassport !=null || @.travelDoc !=null)]'
,N'{
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry",
"macauId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"travelDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''TRAVELDOC'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
}'
,'Payor''s nationality proof not found. (Declared Nationality = Macau)'
,'N','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified
--IFP_23_RL01_NBUW_3952 modified end

--WDL0335
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0335','PASSPORT_OWN__MO__SUP'
,N'$[?((@.declaredNationality==''MO'' && @.nonPermId != null) || @.macauPassport != null )]'
,N'{
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"nonPermId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''NONPERM'')]",
"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
}'
,'Policyowner''s nationality proof not found. (Declared Nationality = Macau)'
,'N','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified

--WDL0336
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0336','PASSPORT_INS__MO__SUP'
,N'$[?((@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId && @.declaredNationality==''MO'' && @.nonPermId != null && @.insuredAge >= 18)  || @.macauPassport != null )]'
,N'{
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"nonPermId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''NONPERM'')]",
"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]",
"insuredAge": "$[0].appData.extParams.insuredAge"
}'
,'Insured''s nationality proof not found. (Declared Nationality = Macau)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added

--WDL0337
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0337','PASSPORT_PYR__MO__SUP'
,N'$[?((@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId && @.appliedPayorBenefit != null && @.declaredNationality==''MO'' && @.nonPermId != null) || @.macauPassport != null )]'
,N'{
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"nonPermId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''NONPERM'')]",
"macauPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.idType == ''SUP'')]"
}'
,'Payor''s nationality proof not found. (Declared Nationality = Macau)'
,'N','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);-- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);-- 20230925 IFP_23_RL05_NBUW_11420 modified
--20220830 IFP_22_RL05_NBUW_2063 added end

--WDL0320
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0320','ID,PASSPORT,ENTRY_OWN__CN__SUP'
--,N'$[?(((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'') && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDocName != null || @.passportDocName != null || @.entryDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s nationality proof not found. (Declared Nationality = China)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--20221107 IFP_23_RL01_NBUW_3952 modified start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0320','ID,PASSPORT,ENTRY_OWN__CN__SUP'
--,N'$[?(((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'')) || @.idDocName != null || @.passportDocName != null || @.entryDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry"
--}'
--,'Policyowner''s nationality proof not found. (Declared Nationality = China)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20221107 IFP_23_RL01_NBUW_3952 modified end
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0321
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0321','ID,PASSPORT,ENTRY_INS__CN__SUP'
--,N'$[?((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'') || @.idDoc != null || @.passportDoc != null || @.entryDoc != null)]'
--,N'{
--"idDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
--"passportDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
--"entryDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''INS'' && @.documentType == ''EXIT'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry"
--}'
--,'Insured''s nationality proof not found. (Declared Nationality = China)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--20221107 IFP_23_RL01_NBUW_3952 modified start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0321','ID,PASSPORT,ENTRY_INS__CN__SUP'
--,N'$[?((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2''&& (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDoc != null || @.passportDoc != null || @.entryDoc != null)]'
--,N'{
--"idDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
--"passportDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
--"entryDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''INS'' && @.documentType == ''EXIT'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Insured''s nationality proof not found. (Declared Nationality = China)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20221107 IFP_23_RL01_NBUW_3952 modified end
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0322
--20221107 IFP_23_RL01_NBUW_3952 modified start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0322','ID,PASSPORT,ENTRY_PYR__CN__SUP'
--,N'$[?((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'' && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.idDocName != null || @.passportDocName != null || @.entryDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
--"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
--"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry"
--}'
--,'Payor''s nationality proof not found. (Declared Nationality = China)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0320','ID,PASSPORT,ENTRY_OWN__CN__SUP'
,N'$[?((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'' && !(@.legalIDType == ''4'' && @.isMCPermLegId != null)) || @.idDocName != null || @.passportDocName != null || @.entryDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry"
}'
,'Policyowner''s nationality proof not found. (Declared Nationality = China)'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0321','ID,PASSPORT,ENTRY_INS__CN__SUP'
,N'$[?((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'' && !(@.legalIDType == ''4'' && @.isMCPermLegId != null) && !(@.legalIDType == ''6'' && @.legalIDIssuedCountry == ''HK'') && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDoc != null || @.passportDoc != null || @.entryDoc != null)]'
,N'{
"idDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
"passportDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
"entryDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''INS'' && @.documentType == ''EXIT'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')]",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s nationality proof not found. (Declared Nationality = China)'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0322','ID,PASSPORT,ENTRY_PYR__CN__SUP'
,N'$[?((@.legalIDIssuedCountry != ''CN'' && @.declaredNationality == ''CN'' && @.legalIDType != ''2'' && !(@.legalIDType == ''4'' && @.isMCPermLegId != null) && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.idDocName != null || @.passportDocName != null || @.entryDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry"
}'
,'Payor''s nationality proof not found. (Declared Nationality = China)'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);
--20221107 IFP_23_RL01_NBUW_3952 modified end

--WDL0323
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0323','ID,PASSPORT_OWN__US__SUP'
--,N'$[?(((@.legalIDIssuedCountry != ''US'' && @.declaredNationality == ''US'') && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDocName != null || @.passportDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s nationality proof not found. (Declared nationality = USA)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0323','ID,PASSPORT_OWN__US__SUP'
,N'$[?(((@.legalIDIssuedCountry != ''US'' && @.declaredNationality == ''US'')) || @.idDocName != null || @.passportDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry"
}'
,'Policyowner''s nationality proof not found. (Declared nationality = USA)'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0324
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0324','ID,PASSPORT_INS__US__SUP'
--,N'$[?((@.legalIDIssuedCountry != ''US'' && @.declaredNationality == ''US'') || @.idDocName != null || @.passportDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry"
--}'
--,'Insured''s nationality proof not found. (Declared nationality = USA)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0324','ID,PASSPORT_INS__US__SUP'
,N'$[?((@.legalIDIssuedCountry != ''US'' && @.declaredNationality == ''US''&& (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDocName != null || @.passportDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s nationality proof not found. (Declared nationality = USA)'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0325
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0325','ID,PASSPORT_PYR__US__SUP'
,N'$[?((@.legalIDIssuedCountry != ''US'' && @.declaredNationality == ''US'' && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId) && @.planCode != null) || @.idDocName != null || @.passportDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry"
}'
,'Payor''s nationality proof not found. (Declared nationality = USA)'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--WDL0326
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0326','ID,PASSPORT_OWN__OTH__SUP'
--,N'$[?(((@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId) && @.legalIDIssuedCountry != @.declaredNationality && @.declaredNationality != ''HK'' && @.declaredNationality != ''CN'' && @.declaredNationality != ''US'' && @.declaredNationality != ''MO'' && @.declaredNationality != null) || (@.idDocName != null && @.otherCountryForID != null) || (@.passportDocName != null && @.otherCountryForPassport != null))]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.idType == ''SUP'')].docName",
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"otherCountryForID":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
--"otherCountryForPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s nationality Proof not found'

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0326','ID,PASSPORT_OWN__OTH__SUP'
,N'$[?((@.legalIDIssuedCountry != @.declaredNationality && @.declaredNationality != ''HK'' && @.declaredNationality != ''CN'' && @.declaredNationality != ''US'' && @.declaredNationality != ''MO'' && @.declaredNationality != null) || (@.idDocName != null && @.otherCountryForID != null) || (@.passportDocName != null && @.otherCountryForPassport != null))]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
"otherCountryForID":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
"otherCountryForPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry"
}'
,'Policyowner''s nationality Proof not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);-- 20221207 IFP_23_RL04_NBUW_4741 modifed
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null); -- 20221207 IFP_23_RL04_NBUW_4741 added
--20220826 IFP_22_RL05_NBUW_2338 added end

--WDL0327
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0327','ID,PASSPORT_INS__OTH__SUP'
--,N'$[?((@.legalIDIssuedCountry != @.declaredNationality && (@.declaredNationality != ''HK'' && @.declaredNationality != ''CN'' && @.declaredNationality != ''US'' && @.declaredNationality != ''MO'' && @.declaredNationality != null)) || (@.idDocName != null && @.otherCountryForID != null) || (@.passportDocName != null && @.otherCountryForPassport != null))]'
--,N'{
--"idDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.idType == ''SUP'')]",
--"passportDoc":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.idType == ''SUP'')]",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"otherCountryForID":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
--"otherCountryForPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry"
--}'
--,'Insured''s nationality Proof not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0327','ID,PASSPORT_INS__OTH__SUP'
,N'$[?(((@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId) && @.legalIDIssuedCountry != @.declaredNationality && (@.declaredNationality != ''HK'' && @.declaredNationality != ''CN'' && @.declaredNationality != ''US'' && @.declaredNationality != ''MO'' && @.declaredNationality != null)) || (@.idDocName != null && @.otherCountryForID != null) || (@.passportDocName != null && @.otherCountryForPassport != null))]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.idType == ''SUP'')]",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.idType == ''SUP'')]",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"otherCountryForID":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
"otherCountryForPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s nationality Proof not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);-- 20221207 IFP_23_RL04_NBUW_4741 modifed
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null); -- 20221207 IFP_23_RL04_NBUW_4741 added
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0328
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0328','ID,PASSPORT_PYR__OTH__SUP'
,N'$[?((@.legalIDIssuedCountry != @.declaredNationality && (@.declaredNationality != ''HK'' && @.declaredNationality != ''CN'' && @.declaredNationality != ''US'' && @.declaredNationality != ''MO'' && @.declaredNationality != null) && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId) && @.planCode != null) || (@.idDocName != null && @.otherCountryForID != null) || (@.passportDocName != null && @.otherCountryForPassport != null))]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.idType == ''SUP'')].docName",
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.idType == ''SUP'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry",
"otherCountryForID":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
"otherCountryForPassport":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry"
}'
,'Payor''s nationality Proof not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);-- 20221207 IFP_23_RL04_NBUW_4741 modifed
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null); -- 20221207 IFP_23_RL04_NBUW_4741 added
--WDL0329
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0329','ID_OWN____LEG'
--,N'$[?((@.declaredNationality == ''CN'' && @.legalIDType == ''2'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.idType == ''LEG'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Legal ID is HK Perm ID but Nationality is China. Please follow up in NBWB.'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0329','ID_OWN____LEG'
,N'$[?((@.declaredNationality == ''CN'' && @.legalIDType == ''2'' ) || @.idDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.idType == ''LEG'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType"
}'
,'Legal ID is HK Perm ID but Nationality is China. Please follow up in NBWB.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0330
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0330','ID_INS____LEG'
--,N'$[?((@.declaredNationality == ''CN'' && @.legalIDType == ''2'') || @.idDocName != null)]'
--,N'{
--"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.idType == ''LEG'')].docName",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType"
--}'
--,'Legal ID is HK Perm ID but Nationality is China. Please follow up in NBWB.'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0330','ID_INS____LEG'
,N'$[?((@.declaredNationality == ''CN'' && @.legalIDType == ''2''&& (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.idDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.idType == ''LEG'')].docName",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Legal ID is HK Perm ID but Nationality is China. Please follow up in NBWB.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL0331
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0331','ID_PYR____LEG'
,N'$[?((@.declaredNationality == ''CN'' && @.legalIDType == ''2'' && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.idDocName != null)]'
,N'{
"idDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.idType == ''LEG'')].docName",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType"
}'
,'Legal ID is HK Perm ID but Nationality is China. Please follow up in NBWB.'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--20220808 IFP_22_RL05_NBUW_1785 add start
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0332','RELATIONPF_OWN___LGD'
,N'$[?(@.doc != null || @.relationshipWithInsured != null)]'
,N'{
"doc":"$[0].appData.subDoc.workDocList[?(@.docType == ''RELATIONPF'' && @.role == ''OWN'' && @.documentType==''LGD'')]",
"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BLG'')]"
}'
,'Beneficiary Relationship is Legal Guardian, please obtain legal guardian document.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0333','RELATIONPF_OWN,INS___MCT'
,N'$[?(@.doc != null || @.relationshipWithInsured != null)]'
,N'{
"doc":"$[0].appData.subDoc.workDocList[?(@.docType == ''RELATIONPF'' && @.documentType==''MCT'' && (@.role == ''OWN'' || @.role == ''INS''))]",
"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BMP'')]"
}'
,'Beneficiary Relationship is Same-sex Married Partner, please obtain marriage certificate.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0334','RELATIONPF_INS___MCT'
,N'$[?(@.doc != null || @.relationshipWithInsured != null)]'
,N'{
"doc":"$[0].appData.subDoc.workDocList[?(@.docType == ''RELATIONPF'' && @.role == ''INS'' && @.documentType==''MCT'')]",
"relationshipWithInsured":"$[0].appData.ownerList[?(@.relationshipWithInsured == ''29'')]"
}'
,'Policyowner Relationship is Same-sex Married Partner, please obtain marriage certificate.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220808 IFP_22_RL05_NBUW_1785 add end

--WDL1608
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1608','LANDING_OWN'
--,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.landingDocName != null)]'
--,N'{
--"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''OWN'')].docName",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s Landing Slip not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220916 IFP_22_RL05_NBUW_3556 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1608','LANDING_OWN'
--,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' ) || @.landingDocName != null)]'
--,N'{
--"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''OWN'')].docName",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType"
--}'
--,'Policyowner''s Landing Slip not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1608','LANDING_OWN'
,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' && (@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' )) || @.landingDocName != null)]'
,N'{
"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''OWN'')].docName",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB"
}'
,'Policyowner''s Landing Slip not found'
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified
--20220916 IFP_22_RL05_NBUW_3556 added end
--WDL1609
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1609','LANDING_INS'
--,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' && @.insuredAge >= 18) || @.landingDocName != null)]'
--,N'{
--"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''INS'')].docName",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"insuredAge":"$[0].appData.extParams.insuredAge"
--}'
--,'Insured''s Landing Slip not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
--20220916 IFP_22_RL05_NBUW_3556 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1609','LANDING_INS'
--,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' && @.insuredAge >= 18 && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.landingDocName != null)]'
--,N'{
--"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''INS'')].docName",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"insuredAge":"$[0].appData.extParams.insuredAge",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Insured''s Landing Slip not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1609','LANDING_INS'
,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' && @.insuredAge >= 18 && (@.ownerClientId != null && (@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' ) && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.landingDocName != null)]'
,N'{
"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''INS'')].docName",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"insuredAge":"$[0].appData.extParams.insuredAge",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB"
}'
,'Insured''s Landing Slip not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220916 IFP_22_RL05_NBUW_3556 added end
--WDL1610
--20220916 IFP_22_RL05_NBUW_3556 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1610','LANDING_PYR'
--,N'$[?((@.legalIDType != ''2'' && @.legalIDType != ''3'' && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.landingDocName != null)]'
--,N'{
--"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''PYR'')].docName",
--"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
--"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType"
--}'
--,'Payor''s Landing Slip not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1610','LANDING_PYR'
,N'$[?((@.legalIDType != ''2'' && @.ownerClientId != null && (@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'' || @.isHKCNCBI == ''Y''|| @.isHKSCB == ''Y'' ) && @.legalIDType != ''3'' && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.landingDocName != null)]'
,N'{
"landingDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''LANDING'' && @.role == ''PYR'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB"
}'
,'Payor''s Landing Slip not found'
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified
--20220916 IFP_22_RL05_NBUW_3556 added end
--WDL1611
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1611','PASSPORT_OWN__US__SUP'
--,N'$[?((@.declaredNationality == ''US'' && @.legalIDIssuedCountry == ''US'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.passportDocName != null)]'
--,N'{
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s Entry Proof not found. (Declared Nationality = USA)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1611','PASSPORT_OWN__US__SUP'
,N'$[?((@.declaredNationality == ''US'' && @.legalIDIssuedCountry == ''US'' ) || @.passportDocName != null)]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry"
}'
,'Policyowner''s Entry Proof not found. (Declared Nationality = USA)'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL1612
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1612','PASSPORT_INS__US__SUP'
--,N'$[?((@.declaredNationality == ''US'' && @.legalIDIssuedCountry == ''US'' && @.insuredAge >= 18) || @.passportDocName != null)]'
--,N'{
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"insuredAge":"$[0].appData.extParams.insuredAge"
--}'
--,'Insured''s Entry Proof not found. (Declared Nationality = USA)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1612','PASSPORT_INS__US__SUP'
,N'$[?((@.declaredNationality == ''US'' && @.legalIDIssuedCountry == ''US'' && @.insuredAge >= 18 && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.passportDocName != null)]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"insuredAge":"$[0].appData.extParams.insuredAge",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s Entry Proof not found. (Declared Nationality = USA)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL1613
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1613','PASSPORT_PYR__US__SUP'
,N'$[?((@.declaredNationality == ''US'' && @.legalIDIssuedCountry == ''US'' && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.passportDocName != null)]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''US'' && @.idType == ''SUP'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry"
}'
,'Payor''s Entry Proof not found. (Declared Nationality = USA)'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--WDL1614
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1614','PASSPORT,ENTRY_OWN__CN__SUP'
--,N'$[?((@.declaredNationality == ''CN'' && @.legalIDIssuedCountry == ''CN'' && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.passportDocName != null || @.entryDocName != null)]'
--,N'{
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s Entry Proof not found. (Declared Nationality = China)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1614','PASSPORT,ENTRY_OWN__CN__SUP'
,N'$[?((@.declaredNationality == ''CN'' && @.legalIDIssuedCountry == ''CN'' ) || @.passportDocName != null || @.entryDocName != null)]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''OWN'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry"
}'
,'Policyowner''s Entry Proof not found. (Declared Nationality = China)'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL1615
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1615','PASSPORT,ENTRY_INS__CN__SUP'
--,N'$[?((@.declaredNationality == ''CN'' && @.legalIDIssuedCountry == ''CN'' && @.insuredAge >= 18) || @.passportDocName != null || @.entryDocName != null)]'
--,N'{
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"insuredAge":"$[0].appData.extParams.insuredAge"
--}'
--,'Insured''s Entry Proof not found. (Declared Nationality = China)'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1615','PASSPORT,ENTRY_INS__CN__SUP'
,N'$[?((@.declaredNationality == ''CN'' && @.legalIDIssuedCountry == ''CN'' && @.insuredAge >= 18 && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || @.passportDocName != null || @.entryDocName != null)]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''INS'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"insuredAge":"$[0].appData.extParams.insuredAge",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s Entry Proof not found. (Declared Nationality = China)'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL1616
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1616','PASSPORT,ENTRY_PYR__CN__SUP'
,N'$[?((@.declaredNationality == ''CN'' && @.legalIDIssuedCountry == ''CN'' && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || @.passportDocName != null || @.entryDocName != null)]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''PYR'' && @.issuedCountry == ''CN'' && @.idType == ''SUP'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry"
}'
,'Payor''s Entry Proof not found. (Declared Nationality = China)'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--WDL1617
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1617','PASSPORT_OWN__OTH__SUP'
--,N'$[?((@.declaredNationality == @.legalIDIssuedCountry && (@.legalIDType == ''6'' || @.legalIDType == ''9'') && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || (@.passportDocName != null && @.otherCountry != null))]'
--,N'{
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
--"otherCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
--,'Policyowner''s Entry Proof not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1617','PASSPORT_OWN__OTH__SUP'
,N'$[?((@.declaredNationality == @.legalIDIssuedCountry && (@.legalIDType == ''6'' || @.legalIDType == ''9'')) || (@.passportDocName != null && @.otherCountry != null))]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdCountry",
"otherCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''OWN'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry"
}'
,'Policyowner''s Entry Proof not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);-- 20221207 IFP_23_RL04_NBUW_4741 modifed
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null); -- 20221207 IFP_23_RL04_NBUW_4741 added
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL1618
--20220826 IFP_22_RL05_NBUW_2338 added start
--INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1618','PASSPORT_INS__OTH__SUP'
--,N'$[?((@.declaredNationality == @.legalIDIssuedCountry && (@.legalIDType == ''6'' || @.legalIDType == ''9'') && @.insuredAge >= 18) || (@.passportDocName != null && @.otherCountry != null))]'
--,N'{
--"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.idType == ''SUP'')].docName",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
--"insuredAge":"$[0].appData.extParams.insuredAge",
--"otherCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry"
--}'
--,'Insured''s Entry Proof not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1618','PASSPORT_INS__OTH__SUP'
,N'$[?((@.declaredNationality == @.legalIDIssuedCountry && (@.legalIDType == ''6'' || @.legalIDType == ''9'') && @.insuredAge >= 18 && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId)) || (@.passportDocName != null && @.otherCountry != null))]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && @.idType == ''SUP'')].docName",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry",
"insuredAge":"$[0].appData.extParams.insuredAge",
"otherCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''INS'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Insured''s Entry Proof not found'
--,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);//20221115 IFP_23_RL01_NBUW_4566 modifed
,'N','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);--//20221115 IFP_23_RL01_NBUW_4566 added
--20220826 IFP_22_RL05_NBUW_2338 added end
--WDL1619
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1619','PASSPORT_PYR__OTH__SUP'
,N'$[?((@.declaredNationality == @.legalIDIssuedCountry && (@.legalIDType == ''6'' || @.legalIDType == ''9'') && @.planCode != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId)) || (@.passportDocName != null && @.otherCountry != null))]'
,N'{
"passportDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && @.idType == ''SUP'')].docName",
"planCode":"$[0].appData.coverage.coveragePlanList[?(@.planCode == ''PB'')].planCode",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdCountry",
"otherCountry":"$[0].appData.subDoc.workDocList[?(@.docType == ''PASSPORT'' && @.role == ''PYR'' && (@.issuedCountry != ''HK'' && @.issuedCountry != ''MO'' && @.issuedCountry != ''US'' && @.issuedCountry != ''CN'') )].issuedCountry"
}'
,'Payor''s Entry Proof not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);-- 20221207 IFP_23_RL04_NBUW_4741 modifed
,'N','24-Apr-2023','SYSTEM','24-Apr-2023','SYSTEM',null,null); -- 20221207 IFP_23_RL04_NBUW_4741 added

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL2401','ID,ENTRY_*'
,N'$[?(@.isBackScanDocument ==''Y'')]'
,N'{
"isBackScanDocument":"$[0].appData.extParams.docValidationParam.isBackScanDocument"
}'
,'Backscan document cannot be defined Please follow up in NBWB'
,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null);

--20220705 IFP_22_RL04_NBUW_1480 added end

--20220808 IFP_22_RL05_NBUW_1785 add start
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1204','DEIBENE'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?(@.docType != null || (@.isHKPolicy == ''Y'' &&  @.relationshipWithInsured != null))]'
,N'$[?(@.docType != null || ((@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKCNCBI == ''Y'' || @.isHKSCB == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'') && @.relationshipWithInsured != null))]'
--IFP_23_RL04_NBUW_7176 start
--,N'{
--"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIBENE'')]",
--"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BDP'')]",
--"isHKPolicy":"$[0].appData.extParams.isHKPolicy"
--}'
--,N'{
--"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIBENE'')]",
--"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BDP'')]",
--"isHKPolicy":"$[0].appData.extParams.isHKAgency"
--}'
--IFP_23_RL04_NBUW_7176 end
,N'{
"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIBENE'')]",
"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BDP'')]",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent"
}'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified end
,'Beneficiary Relationship is Cohabiting Domestic Partner, please obtain Form U58.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1205','DEIBENE'
,N'$[?(@.docType != null || (@.isMCPolicy == ''Y'' &&  @.relationshipWithInsured != null))]'
--IFP_23_RL04_NBUW_7176 start
--,N'{
--"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIBENE'')]",
--"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BDP'')]",
--"isMCPolicy":"$[0].appData.extParams.isMCPolicy"
--}'
,N'{
"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIBENE'')]",
"relationshipWithInsured":"$[0].appData.beneficiaryList[?(@.relationshipWithInsured == ''BDP'')]",
"isMCPolicy":"$[0].appData.extParams.isMCAgency"
}'
--IFP_23_RL04_NBUW_7176 end
,'Beneficiary Relationship is Cohabiting Domestic Partner, please obtain Form U58_M.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1206','DEIOWNER'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?(@.docType != null || (@.isHKPolicy == ''Y'' &&  @.relationshipWithInsured != null))]'
,N'$[?(@.docType != null || ((@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKCNCBI == ''Y'' || @.isHKSCB == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'') &&  @.relationshipWithInsured != null))]'
--IFP_23_RL04_NBUW_7176 start
--,N'{
--"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIOWNER'')]",
--"relationshipWithInsured":"$[0].appData.ownerList[?(@.relationshipWithInsured == ''29'')]",
--"isHKPolicy":"$[0].appData.extParams.isHKPolicy"
--}'
--,N'{
--"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIOWNER'')]",
--"relationshipWithInsured":"$[0].appData.ownerList[?(@.relationshipWithInsured == ''29'')]",
--"isHKPolicy":"$[0].appData.extParams.isHKAgency"
--}'
--IFP_23_RL04_NBUW_7176 end
,N'{
"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIOWNER'')]",
"relationshipWithInsured":"$[0].appData.ownerList[?(@.relationshipWithInsured == ''29'')]",
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent"
}'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified end
,'Policyowner Relationship is Same-sex Married Partner, please obtain Form U60.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1207','DEIOWNER'
,N'$[?(@.docType != null || (@.isMCPolicy == ''Y'' &&  @.relationshipWithInsured != null))]'
--IFP_23_RL04_NBUW_7176 start
--,N'{
--"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIOWNER'')]",
--"relationshipWithInsured":"$[0].appData.ownerList[?(@.relationshipWithInsured == ''29'')]",
--"isMCPolicy":"$[0].appData.extParams.isMCPolicy"
--}'
,N'{
"docType":"$[0].appData.subDoc.workDocList[?(@.docType == ''DEIOWNER'')]",
"relationshipWithInsured":"$[0].appData.ownerList[?(@.relationshipWithInsured == ''29'')]",
"isMCPolicy":"$[0].appData.extParams.isMCAgency"
}'
--IFP_23_RL04_NBUW_7176 end
,'Policyowner Relationship is Same-sex Married Partner, please obtain Form U60_M.'
,'Y','06-Nov-2022','SYSTEM','06-Nov-2022','SYSTEM',null,null);

--20220808 IFP_22_RL05_NBUW_1785 add end

--20220805 IFP_22_RL05_NBUW_1540 added end
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL2501','IFSPF'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?((@.isHKAgency == ''Y'' && @.premFnaInd == ''Y''))]'
,N'$[?(@.premFnaInd == ''Y'')]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"premFnaInd":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].premFinInd"
--}'
,N'{
"premFnaInd":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].premFinInd"
}'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified end
,'Important Facts Statement - Premium Financing (IFS-PF) not found'
,'Y','02-Jan-2022','SYSTEM','02-Jan-2022','SYSTEM',null,null);
--20220805 IFP_22_RL05_NBUW_1540 added end

--20220914 IFP_22_RL05_NBUW_2338 added start
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0338','ID,PASSPORT_OWN,INS___NOTBIRTH_LEG'
,N'$[?(((@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId == @.insuredClientId)) || @.id != null || @.passport != null )]'
,N'{
"id":"$[0].appData.subDoc.workDocList[?(@.role== ''OWN'' && @.docType == ''ID'' && @.idType == ''LEG'' && (@.documentType != ''BIRTH''))]",
"passport":"$[0].appData.subDoc.workDocList[?(@.role== ''OWN'' && @.docType == ''PASSPORT'' && @.idType == ''LEG''&& (@.documentType != ''BIRTH''))]",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
,'Policyowner''s Legal ID/PASSPORT not found'
--,'Y','24-Jul-2022','SYSTEM','24-Jul-2022','SYSTEM',null,null); //IFP_23_RL05_NBUW_11288 commented
--20220914 IFP_22_RL05_NBUW_2338 added end
,'N','06-Nov-2023','SYSTEM','06-Nov-2023','SYSTEM',null,null); --IFP_23_RL05_NBUW_11288 added

--20221111 IFP_23_RL01_NBUW_3619 added start
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1620','ENTRY_OWN__MO_LAND_'
,N'$[?((@.isMCAgency == ''Y'' && !(@.legalIDType == ''4'' && @.permId != null) && !(@.legalIDType == ''4'' && @.nonPermId != null)) || @.entryDocName != null)]'
,N'{
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
"permId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"nonPermId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''NONPERM'')]",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''LAND'')].docName"
}'
,'Policyowner''s Macau Landing Slip not found'
,'N','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1621','ENTRY_INS__MO_LAND_'
,N'$[?((@.isMCAgency == ''Y'' && @.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId && @.insuredAge >= 18 && !(@.legalIDType == ''4'' && @.permId != null) && !(@.legalIDType == ''4'' && @.nonPermId != null)) || @.entryDocName != null)]'
,N'{
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId",
"insuredAge": "$[0].appData.extParams.insuredAge",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"permId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"nonPermId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''NONPERM'')]",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''LAND'')].docName"
}'
,'Insured''s Macau Landing Slip not found'
,'N','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL1622','ENTRY_PYR__MO_LAND_'
,N'$[?((@.isMCAgency == ''Y'' && @.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId && @.appliedPayorBenefit != null && !(@.legalIDType == ''4'' && @.permId != null) && !(@.legalIDType == ''4'' && @.nonPermId != null)) || @.entryDocName != null)]'
,N'{
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"appliedPayorBenefit":"$[0].appData.coverage.coveragePlanList[?(@.planCode==''PB'')]",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
"permId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"nonPermId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''NONPERM'')]",
"entryDocName":"$[0].appData.subDoc.workDocList[?(@.docType == ''ENTRY'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''LAND'')].docName"
}'
,'Payor''s Macau Landing Slip not found'
,'N','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 added
-- ,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null); -- 20230925 IFP_23_RL05_NBUW_11420 modified
--20221111 IFP_23_RL01_NBUW_3619 added end

--20221107 IFP_23_RL01_NBUW_3952 added start
INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0339',''
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?(@.declaredNationality == ''CN'' && @.legalIDType == ''4'' &&  @.isMCPermLegId != null)]'
,N'$[?(@.eligibelChnl != null && (@.isMCAgency == ''Y'' || @.isMCBroker == ''Y'') && @.declaredNationality == ''CN'' && @.legalIDType == ''4'' && @.isMCPermLegId != null)]'
--,N'{
--"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
--"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
--"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]"
--}'
,N'{
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"isMCBroker":"$[0].appData.extParams.isMCBroker",
"declaredNationality":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].nationality.country",
"legalIDType":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].legalIdType",
"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''OWN'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]"
}'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified end
,'Legal ID is Macau Perm ID but Nationality is China. Please follow up in NBWB.'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0340',''
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?(@.declaredNationality == ''CN'' && @.legalIDType == ''4'' && @.isMCPermLegId != null && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId))]'
,N'$[?(@.eligibelChnl != null && (@.isMCAgency == ''Y'' || @.isMCBroker == ''Y'') && @.declaredNationality == ''CN'' && @.legalIDType == ''4'' && @.isMCPermLegId != null && (@.ownerClientId != null && @.insuredClientId != null && @.ownerClientId != @.insuredClientId))]'
--,N'{
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
--}'
,N'{
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"isMCBroker":"$[0].appData.extParams.isMCBroker",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''INS'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"insuredClientId":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].clientId"
}'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified end
,'Legal ID is Macau Perm ID but Nationality is China. Please follow up in NBWB.'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0341',''
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?(@.declaredNationality == ''CN'' && @.legalIDType == ''4'' && @.isMCPermLegId != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId))]'
,N'$[?(@.eligibelChnl != null && (@.isMCAgency == ''Y'' || @.isMCBroker == ''Y'') && @.declaredNationality == ''CN'' && @.legalIDType == ''4'' && @.isMCPermLegId != null && (@.ownerClientId != null && @.payorClientId != null && @.ownerClientId != @.payorClientId))]'
--,N'{
--"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
--"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
--"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
--"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
--"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]"
--}'
,N'{
"eligibelChnl":"$[?(@.appData.chnlCode== null || @.appData.chnlCode== ''E'')].appData.chnlCode",
"isMCAgency":"$[0].appData.extParams.isMCAgency",
"isMCBroker":"$[0].appData.extParams.isMCBroker",
"ownerClientId":"$[0].appData.ownerList[?(@.clientRole == ''O01'' || @.clientRole == ''J01'')].clientId",
"payorClientId":"$[0].appData.payorList[?(@.clientRole == ''P01'')].clientId",
"declaredNationality":"$[0].appData.payorList[?(@.clientRole == ''P01'')].nationality.country",
"legalIDType":"$[0].appData.payorList[?(@.clientRole == ''P01'')].legalIdType",
"isMCPermLegId":"$[0].appData.subDoc.workDocList[?(@.docType == ''ID'' && @.role == ''PYR'' && @.issuedCountry == ''MO'' && @.documentType == ''PERM'')]"
}'
--20240229 IFP_24_RL03_NBUW_14108_16290 modified end
,'Legal ID is Macau Perm ID but Nationality is China. Please follow up in NBWB.'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);

INSERT into DOC_RULE_CTRL values ('DOC_CTRL','WDL0342',''
--20240229 IFP_24_RL03_NBUW_14108_16290 modified start
--,N'$[?(@.isHKAgency == ''Y'' && @.declaredNationality == ''CN'' && @.legalIDType == ''6'' && @.legalIDIssuedCountry == ''HK'')]'
,N'$[?((@.isHKAgency == ''Y'' || @.isHKBroker == ''Y'' || @.isHKCNCBI == ''Y'' || @.isHKSCB == ''Y'' || @.isHKDBS == ''Y'' || @.isHKCorporateAgent == ''Y'') && @.declaredNationality == ''CN'' && @.legalIDType == ''6'' && @.legalIDIssuedCountry == ''HK'')]'
--,N'{
--"isHKAgency":"$[0].appData.extParams.isHKAgency",
--"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
--"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
--"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry"
--}'
,N'{
"isHKAgency":"$[0].appData.extParams.isHKAgency",
"isHKBroker":"$[0].appData.extParams.isHKBroker",
"isHKCNCBI":"$[0].appData.extParams.isHKCNCBI",
"isHKSCB":"$[0].appData.extParams.isHKSCB",
"isHKDBS":"$[0].appData.extParams.isHKDBS",
"isHKCorporateAgent":"$[0].appData.extParams.isHKCorporateAgent",
"declaredNationality":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].nationality.country",
"legalIDType":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdType",
"legalIDIssuedCountry":"$[0].appData.insuredList[?(@.clientRole == ''I01'')].legalIdCountry"
}'
--20240223 IFP_24_RL03_NBUW_14108_16290 modified end
,'Legal ID is HK Passport / Other Travel Doc but Nationality is China. Please follow up in NBWB.'
,'Y','02-Jan-2023','SYSTEM','02-Jan-2023','SYSTEM',null,null);
--20221107 IFP_23_RL01_NBUW_3952 added end