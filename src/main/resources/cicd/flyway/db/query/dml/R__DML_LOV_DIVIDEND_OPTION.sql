------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'DIVIDEND_OPTION';

insert into list_of_value values('DIVIDEND_OPTION',N'[{"code":"2","engDesc":"Leave on deposit","chinDesc":"","attributes":[{"nbwbCode":"2","eposValue":""}]},{"code":"9","engDesc":"Bonus protection","chinDesc":"","attributes":[{"nbwbCode":"9","eposValue":""}]},{"code":"C","engDesc":"Paid At Maturity","chinDesc":"","attributes":[{"nbwbCode":"C","eposValue":""}]},{"code":"4","engDesc":"Premium Reduction","chinDesc":"","attributes":[{"nbwbCode":"4","eposValue":""}]},{"code":"D","engDesc":"Paid Up Additions","chinDesc":"","attributes":[{"nbwbCode":"D","eposValue":""}]},{"code":"E","engDesc":"Pay out annually","chinDesc":"","attributes":[{"nbwbCode":"E","eposValue":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
