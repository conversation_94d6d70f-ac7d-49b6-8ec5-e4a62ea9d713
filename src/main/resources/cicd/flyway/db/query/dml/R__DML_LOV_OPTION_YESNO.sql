------------------------------------------------------------------------------------------
-- Mod. Date : 9 Aug 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL05_NBUW_2022
-- Mod. Desc : [DEV-BE] Data Update – Default Payout (NBUWS/STP Validation)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'OPTION_YESNO';

insert into list_of_value values('OPTION_YESNO',N'[{"code":"Y","engDesc":"YES","chinDesc":"","attributes":[{"nbwbCode":"Y","eposCode":"Y"}]}, {"code":"N","engDesc":"NO","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":"N"}]}]','20-NOV-2022',NULL,'SYSTEM','20-NOV-2022');

commit;
