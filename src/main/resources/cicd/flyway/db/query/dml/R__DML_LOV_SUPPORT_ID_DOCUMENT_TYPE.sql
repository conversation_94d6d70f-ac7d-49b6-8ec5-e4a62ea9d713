------------------------------------------------------------------------------------------
-- Mod. Date : 15 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON><PERSON> Wu
-- Mod. ref  : IFP_22_RL04_NBUW_1509
-- Mod. Desc : Multiple Client ID - STP rule & Field mapping
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'SUPPORT_ID_DOCUMENT_TYPE';

insert into list_of_value values('SUPPORT_ID_DOCUMENT_TYPE',N'[
{"code":"1","engDesc":"HK Re-Entry Permit","chinDesc":"香港特別行政區回港證","attributes":[{"nbwbCode":"1","eposCode":"HK_ENTRY"}]},
{"code":"2","engDesc":"HK Passport/Other Travel Doc","chinDesc":"香港特別行政區護照 / 其他旅遊證件","attributes":[{"nbwbCode":"2","eposCode":"HK_PASSPORT"}]},
{"code":"3","engDesc":"China ID / Birth Cert","chinDesc":"中國內地居民身份證","attributes":[{"nbwbCode":"3","eposCode":"PRC_ID"}]},
{"code":"4","engDesc":"China Passport","chinDesc":"中國護照","attributes":[{"nbwbCode":"4","eposCode":"PRC_PASSPORT"}]},
{"code":"5","engDesc":"China Exit-Entry Permit","chinDesc":"往來港澳通行證","attributes":[{"nbwbCode":"5","eposCode":"PRC_ENTRY"}]},
{"code":"6","engDesc":"Macau ID / Birth Cert","chinDesc":"澳門居民身份證","attributes":[{"nbwbCode":"6","eposCode":"MACAU_ID"}]},
{"code":"7","engDesc":"Macau Passport","chinDesc":"其他身份證","attributes":[{"nbwbCode":"7","eposCode":"MACAU_PASSPORT"}]},
{"code":"8","engDesc":"USA ID / Birth Cert","chinDesc":"美國身份證明文件","attributes":[{"nbwbCode":"8","eposCode":"USA_ID"}]},
{"code":"9","engDesc":"USA Passport","chinDesc":"美國護照","attributes":[{"nbwbCode":"9","eposCode":"USA_PASSPORT"}]},
{"code":"10","engDesc":"Other Country Passport","chinDesc":"護照","attributes":[{"nbwbCode":"10","eposCode":"OTHER_PASSPORT"}]},
{"code":"11","engDesc":"Other Country ID / Birth Cert","chinDesc":"其他身份證","attributes":[{"nbwbCode":"11","eposCode":"OTHER_ID"}]}]',
'24-Jul-2022',NULL,'SYSTEM','24-Jul-2022');
commit;
