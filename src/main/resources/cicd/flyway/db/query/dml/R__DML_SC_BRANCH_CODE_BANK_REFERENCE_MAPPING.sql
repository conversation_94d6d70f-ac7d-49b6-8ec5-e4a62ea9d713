------------------------------------------------------------------------------------------
-- Mod. Date : 21 Mar 2024
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_24_RL03_NBUW_15934_18051
-- Mod. Desc : "Bank Reference" under Agent tab - Validations
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'BRANCH_CODE_BANK_REFERENCE_MAPPING';

-- branch code & bank reference checking list/match rules, branchCode + include(root-level) must be unique in config list
insert into sys_config values(
    'BRANCH_CODE_BANK_REFERENCE_MAPPING',
    CAST(N'[' AS nvarchar(max))
        -- the `include=true`, means when branchCode is someone, `include=false` means when branchCode <> someone
        -- the below config means when branchCode = 82660, then cifNo/customerSegment can not be blank
        +N'{"branchCode": "82660", "include": true, "cifNo": {"blank": false}, "customerSegment": {"blank": false}}'
        +N',{"branchCode": "82660", "include": false,"cifNo": {"blank": true}, "customerSegment": {"blank": true}}'
        -- when you config mapping item as below, it means when branchCode = 826B0 and customerSegment is inputted, then customerSegment can not be PB, the value should be another select value
        -- +N',{"branchCode": "826B0", "include": true,"customerSegment": {"value": "PB", "include": false}}'
        -- the below config means when branchCode = 826B0 and customerSegment is inputted, then customerSegment must be PB
        +N',{"branchCode": "826B0", "include": true,"customerSegment": {"value": "PB", "include": true}}'
        +N',{"branchCode": "826B0", "include": false,"customerSegment": {"blank": true}}'
        --+N',{"branchCode": "896A0", "include": true,"customerSegment": {"value": "RETB|COMB|PRIB|WM", "include": true}}'
        --+N',{"branchCode": "896A0", "include": false,"customerSegment": {"value": "RETB|COMB|PRIB|WM", "include": false}}'
    +N']',
    '21-Jul-2024',
    'SYSTEM',
    '21-Jul-2024'
);

commit;
