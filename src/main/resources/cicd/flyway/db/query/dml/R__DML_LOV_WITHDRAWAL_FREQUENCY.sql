------------------------------------------------------------------------------------------
-- Mod. Date : 21 Apr 2023
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_2499_2249
-- Mod. Desc : Data Update - Regular Payout for Saving Products
-------------------------------------------------------------------------------------------
-- Mod. Date : 09 Jun 2023
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_2499_UAT_BUG_10479
-- Mod. Desc : Withdrawal Frequency can't sync to NBWB if product is FlexiForture
-------------------------------------------------------------------------------------------
-- Mod. Date : 07 Mar 2024
-- Mod. By   : (MITDC CD) Jason Zhang
-- Mod. ref  : IFP_24_RL02_NBUW_14110_BE_J
-- Mod. Desc : 2024 Apr Release - 2024 New Product - Genesis (NBUWS)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'WITHDRAWAL_FREQUENCY';
--20230606 IFP_23_RL04_NBUW_2499_UAT_BUG_10479 modified start
--insert into list_of_value values(
--    'WITHDRAWAL_FREQUENCY',
--    N'[
--        {"code":"01","engDesc":"Monthly","chinDesc":"","attributes":[{"nbwbCode": "01","eposCode": "01"}]}
--    ]',
--    '23-JUL-2023',
--    NULL,
--    'SYSTEM',
--    '23-JUL-2023'
--);

-- 20240307 IFP_24_RL02_NBUW_14110_BE_J delete start
--insert into list_of_value values(
--    'WITHDRAWAL_FREQUENCY',
--    CAST(N'[{"code":"1","engDesc":"Monthly","chinDesc":"","attributes":[{"nbwbCode": "1","eposCode": "1"}]}'AS nvarchar(max))
--    +N']'
--    ,'23-JUL-2023'
--    ,NULL
--    ,'SYSTEM'
--    ,'23-JUL-2023'
--);
-- 20240307 IFP_24_RL02_NBUW_14110_BE_J delete start

--20230606 IFP_23_RL04_NBUW_2499_UAT_BUG_10479 modified end

-- 20240307 IFP_24_RL02_NBUW_14110_BE_J add start
insert into list_of_value values(
    'WITHDRAWAL_FREQUENCY',
    CAST(N'[{"code":"1","engDesc":"Monthly","chinDesc":"","attributes":[{"nbwbCode": "1","eposCode": "1"}]}'AS nvarchar(max))
    +N',{"code":"12","engDesc":"Annual","chinDesc":"","attributes":[{"nbwbCode":"12","eposCode":"12"}]}'
    +N']'
    ,'23-JUL-2023'
    ,NULL
    ,'SYSTEM'
    ,'21-Apr-2024'
);
-- 20240307 IFP_24_RL02_NBUW_14110_BE_J add end
commit;
