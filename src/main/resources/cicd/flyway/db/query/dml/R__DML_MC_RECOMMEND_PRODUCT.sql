-------------------------------------------------------------------------------------------
-- Mod. Date : 13 Apr 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL05_JMIFUM_328
-- Mod. Desc : 2022 May Release - AKS Migration Development - hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'MC_RECOMMEND_PRODUCT';

insert into SYS_CONFIG values(
    'MC_RECOMMEND_PRODUCT',
    N'[
       	{
       		"seqNo": "1",
       		"objBuying": "4601",
       		"typeOfInsurance": "4602",
       		"planCode": "4603",
       		"protectPeriodOfRider": "4604",
       		"productSelectedInd": "4605"
       	},
       	{
       		"seqNo": "2",
       		"objBuying": "4606",
       		"typeOfInsurance": "4607",
       		"planCode": "4608",
       		"protectPeriodOfRider": "4609",
       		"productSelectedInd": "4610"
       	},
       	{
       		"seqNo": "3",
       		"objBuying": "4611",
       		"typeOfInsurance": "4612",
       		"planCode": "4613",
       		"protectPeriodOfRider": "4614",
       		"productSelectedInd": "4615"
       	},
       	{
       		"seqNo": "4",
       		"objBuying": "4616",
       		"typeOfInsurance": "4617",
       		"planCode": "4618",
       		"protectPeriodOfRider": "4619",
       		"productSelectedInd": "4620"
       	},
       	{
       		"seqNo": "5",
       		"objBuying": "4621",
       		"typeOfInsurance": "4622",
       		"planCode": "4623",
       		"protectPeriodOfRider": "4624",
       		"productSelectedInd": "4625"
       	},
       	{
       		"seqNo": "6",
       		"objBuying": "4626",
       		"typeOfInsurance": "4627",
       		"planCode": "4628",
       		"protectPeriodOfRider": "4629",
       		"productSelectedInd": "4630"
       	},
       	{
       		"seqNo": "7",
       		"objBuying": "4631",
       		"typeOfInsurance": "4632",
       		"planCode": "4633",
       		"protectPeriodOfRider": "4634",
       		"productSelectedInd": "4635"
       	},
       	{
       		"seqNo": "8",
       		"objBuying": "4636",
       		"typeOfInsurance": "4637",
       		"planCode": "4638",
       		"protectPeriodOfRider": "4639",
       		"productSelectedInd": "4640"
       	},
       	{
       		"seqNo": "9",
       		"objBuying": "4641",
       		"typeOfInsurance": "4642",
       		"planCode": "4643",
       		"protectPeriodOfRider": "4644",
       		"productSelectedInd": "4645"
       	},
       	{
       		"seqNo": "10",
       		"objBuying": "4646",
       		"typeOfInsurance": "4647",
       		"planCode": "4648",
       		"protectPeriodOfRider": "4649",
       		"productSelectedInd": "4650"
       	}
       ]',
    NULL,
    'SYSTEM',
    '03-May-2022');

commit;
