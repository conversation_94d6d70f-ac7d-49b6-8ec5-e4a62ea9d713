------------------------------------------------------------------------------------------
-- Mod. Date : 21 Apr 2023
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_2499_2249
-- Mod. Desc : Data Update - Regular Payout for Saving Products
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'WITHDRAWAL_METHOD';

insert into list_of_value values(
    'WITHDRAWAL_METHOD',
    N'[
        {"code":"AP","engDesc":"Autopay","chinDesc":"","attributes":[{"nbwbCode": "AP","eposCode": "AP"}]},
        {"code":"CQ","engDesc":"Cheque","chinDesc":"","attributes":[{"nbwbCode": "CQ","eposCode": "CQ"}]},
        {"code":"CQ-HK","engDesc":"Cheque (Drawn in HK)","chinDesc":"","attributes":[{"nbwbCode": "CQ-HK","eposCode": "CQ-HK"}]}
    ]',
    '23-JUL-2023',
    NULL,
    'SYSTEM',
    '23-JUL-2023'
);
commit;
