-------------------------------------------------------------------------------------------
--  Mod. Date : 01 Dec 2023
--  Mod. By   : (MITDC CD) <PERSON>
--  Mod. ref  : IFP_24_RL01_NBUW_12952
--  Mod. Desc : 2024 Jan Release - NBUWS validation for large amount validation (Per-life checking)
-------------------------------------------------------------------------------------------
-- Mod. Date : 27 Feb 2024
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_24_RL02_NBUW_14110_BE_B
-- Mod. Desc : 2024 Apr Release - 2024 New Product - Genesis (NBUWS)
-------------------------------------------------------------------------------------------
--  Mod. Date : 26 Nov 2024
--  Mod. By   : (MITDC CD) Parker Song
--  Mod. ref  : IFP_25_RL01_NBUW_29525
--  Mod. Desc : 2025 Jan Release - 2024 New Product - New Saving (NBUWS)
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'CHK_PHI_GIO_PRODUCT';
--IFP_24_RL02_NBUW_14110_BE_B start
--insert into SYS_CONFIG
--values
--	 ('CHK_PHI_GIO_PRODUCT',
--		'[
--		    {"planCode":"PU500","schmCode":"WB011900"},
--		    {"planCode":"PU500","schmCode":"WB051900"},
--		    {"planCode":"PU500","schmCode":"WB101900"},
--		    {"planCode":"PU500","schmCode":"WB021900"},
--		    {"planCode":"PU500","schmCode":"WB012000"},
--		    {"planCode":"PU500","schmCode":"WB052000"},
--		    {"planCode":"PU500","schmCode":"WB102000"},
--		    {"planCode":"PU500","schmCode":"WB022000"},
--		    {"planCode":"PU500","schmCode":"WB051901"},
--		    {"planCode":"PU500","schmCode":"WB101901"},
--		    {"planCode":"PU500","schmCode":"WB052001"},
--            {"planCode":"PU500","schmCode":"WB102001"},
--            {"planCode":"PU500","schmCode":"WB152101"},
--            {"planCode":"PU500","schmCode":"WB012101"},
--            {"planCode":"PU500","schmCode":"WB012202"},
--            {"planCode":"PU500","schmCode":"WB032202"},
--            {"planCode":"PU500","schmCode":"WB052202"},
--            {"planCode":"PU500","schmCode":"WB102202"},
--            {"planCode":"PU500","schmCode":"WB012203"},
--            {"planCode":"PU500","schmCode":"WB032203"}
--        ]',
--		'07-Jan-2024',
--		'SYSTEM',
--		'07-Jan-2024');
--IFP_24_RL02_NBUW_14110_BE_B end
insert into SYS_CONFIG
values
	 ('CHK_PHI_GIO_PRODUCT',
	 CAST(N'[' AS nvarchar(max))
		 +N'{"planCode":"PU500","schmCode":"WB011900"}'
		 +N',{"planCode":"PU500","schmCode":"WB051900"}'
		 +N',{"planCode":"PU500","schmCode":"WB101900"}'
		 +N',{"planCode":"PU500","schmCode":"WB021900"}'
		 +N',{"planCode":"PU500","schmCode":"WB012000"}'
		 +N',{"planCode":"PU500","schmCode":"WB052000"}'
		 +N',{"planCode":"PU500","schmCode":"WB102000"}'
		 +N',{"planCode":"PU500","schmCode":"WB022000"}'
		 +N',{"planCode":"PU500","schmCode":"WB101901"}'
		 +N',{"planCode":"PU500","schmCode":"WB052001"}'
         +N',{"planCode":"PU500","schmCode":"WB102001"}'
         +N',{"planCode":"PU500","schmCode":"WB152101"}'
         +N',{"planCode":"PU500","schmCode":"WB012101"}'
         +N',{"planCode":"PU500","schmCode":"WB012202"}'
         +N',{"planCode":"PU500","schmCode":"WB032202"}'
         +N',{"planCode":"PU500","schmCode":"WB052202"}'
         +N',{"planCode":"PU500","schmCode":"WB102202"}'
         +N',{"planCode":"PU500","schmCode":"WB012203"}'
         +N',{"planCode":"PU500","schmCode":"WB032203"}'
         --IFP_24_RL02_NBUW_14110_BE_B start
         +N',{"planCode":"PU500","schmCode":"WB012400"}'
         +N',{"planCode":"PU500","schmCode":"WB032400"}'
         +N',{"planCode":"PU500","schmCode":"WB052400"}'
         +N',{"planCode":"PU500","schmCode":"WB102400"}'
         +N',{"planCode":"PU500","schmCode":"WB152400"}'
         --IFP_24_RL02_NBUW_14110_BE_B end
         --IFP_25_RL01_NBUW_29525 added start
          +N',{"planCode":"PU500","schmCode":"WB012500"}'
          +N',{"planCode":"PU500","schmCode":"WB022500"}'
          +N',{"planCode":"PU500","schmCode":"WB032500"}'
          +N',{"planCode":"PU500","schmCode":"WB052500"}'
         --IFP_25_RL01_NBUW_29525 added end
        +N']',
		'07-Jan-2024',
		'SYSTEM',
		'05-Jan-2025');
commit;