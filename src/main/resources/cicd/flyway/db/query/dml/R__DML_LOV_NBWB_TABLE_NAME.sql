-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2023
-- Mod. By   : (MITDC CD) Leo Li
-- Mod. ref  : IFP_24_RL01_NBUW_13035
-- Mod. Desc : 2024 Jan Release - Get NBWB Data
-------------------------------------------------------------------------------------------
-- Mod. Date : 10 Jan 2023
-- Mod. By   : (MITDC CD) Leo Li
-- Mod. ref  : IFP_24_RL02_NBUW_12973
-- Mod. Desc : 2024 Apr Release - Add query NB_INFO_BENEFICIARY table setting(Support hk-payment-service call nb-core)
-------------------------------------------------------------------------------------------
delete from list_of_value where LIST_CODE = 'NBWB_TABLE_NAME';

insert into list_of_value values(
    'NBWB_TABLE_NAME',
    CAST(N'[' AS nvarchar(max))
        +N'{"code":"NB_INFO","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"NB_COVERAGE_ROLE","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"NB_CLIENT_PROD_ROLE_LINK","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"NB_FUND","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"NB_PE_RESULT","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"NB_UW_RESULT","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"TPT_NBTS_CHANGE_DTLS","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"TPT_NBTS_POLICY","engDesc":"","chinDesc":"","attributes":[{}]}'
        +N',{"code":"NB_INFO_AGENT","engDesc":"","chinDesc":"","attributes":[{}]}'
        --IFP_24_RL02_NBUW_12973 added start
        +N',{"code":"NB_INFO_BENEFICIARY","engDesc":"","chinDesc":"","attributes":[{}]}'
        --IFP_24_RL02_NBUW_12973 added end
     +N']',
     '07-Jan-2024',
     NULL,
     'SYSTEM',
     '07-Jan-2024'
);

commit;
