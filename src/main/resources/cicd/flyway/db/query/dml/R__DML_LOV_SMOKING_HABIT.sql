------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'SMOKING_HABIT';

insert into list_of_value values('SMOKING_HABIT',N'[{"code":"UNDER_5_CPD","engDesc":"Occasional or social smoker or less than 5 cigarettes per day","chinDesc":"偶然或在社交場合上吸煙或每日少於5支","attributes":[{"nbwbCode":"S","eposCode":"UNDER_5_CPD"}]}, {"code":"UNDER_30_CPD","engDesc":"Up to 30 cigarettes per day","chinDesc":"每日不超過於30支","attributes":[{"nbwbCode":"S","eposCode":"UNDER_30_CPD"}]}, {"code":"OVER_30_CPD","engDesc":"More than 30 cigarettes per day","chinDesc":"每日30支以上","attributes":[{"nbwbCode":"S","eposCode":"OVER_30_CPD"}]}, {"code":"UNDER_1_CPM_CIGAR","engDesc":"Cigar: 1 stick or below per month","chinDesc":"雪茄: 每月1支或以下","attributes":[{"nbwbCode":"S","eposCode":"UNDER_1_CPM_CIGAR"}]}, {"code":"OVER_1_CPM_CIGAR","engDesc":"Cigar: More than 1 stick per month","chinDesc":"雪茄: 每月超過1支","attributes":[{"nbwbCode":"S","eposCode":"OVER_1_CPM_CIGAR"}]}, {"code":"ECIGARETTE","engDesc":"Electronic cigarette","chinDesc":"電子煙","attributes":[{"nbwbCode":"S","eposCode":"ECIGARETTE"}]}, {"code":"NICOTINE_REPLACEMENT","engDesc":"Nicotine replacement product (e.g. nicotine patches, nicotine gum etc.)","chinDesc":"尼古丁補充劑產品(例如: 戒煙貼或戒煙香口膠)","attributes":[{"nbwbCode":"S","eposCode":"NICOTINE_REPLACEMENT"}]}, {"code":"MULTI_SELECT","engDesc":"Cigar and any of followings: cigarettes, electronic cigarette, nicotine replacement or other tobacco products","chinDesc":"雪茄及以下任何產品: 香煙、電子煙、尼古丁補充劑產品或其他煙草製品","attributes":[{"nbwbCode":"S","eposCode":"MULTI_SELECT"}]}, {"code":"NEVER_SMOKE","engDesc":"Non-Smoker","chinDesc":"非吸煙者","attributes":[{"nbwbCode":"N","eposCode":"NEVER_SMOKE"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
