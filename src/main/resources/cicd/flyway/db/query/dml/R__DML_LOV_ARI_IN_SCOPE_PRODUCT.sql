-------------------------------------------------------------------------------------------
 -- Mod. Date : 19 Apr 2023
 -- Mod. By   : (MITDC CD) <PERSON>
 -- Mod. ref  : IFP_23_RL04_NBUW_7176
 -- Mod. Desc : 2023 Jul Release - Data Capture & Update - Plan and Coverage (ILAS)
--------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'ARI_IN_SCOPE_PRODUCT';

insert into LIST_OF_VALUE
values(
        'ARI_IN_SCOPE_PRODUCT',
        CAST(N'[' AS nvarchar(max))
		    +N'{"code":"MV510","engDesc":"Alpha Regular Investor, Premium Payment Period 10 Years","chinDesc":"","attributes":[{}]}'
            +N',{"code":"MV520","engDesc":"Alpha Regular Investor, Premium Payment Period 20 Years","chinDesc":"","attributes":[{}]}'
            +N',{"code":"MV525","engDesc":"Alpha Regular Investor, Premium Payment Period 25 Years","chinDesc":"","attributes":[{}]}'
            +N',{"code":"MV530","engDesc":"Alpha Regular Investor, Premium Payment Period 30 Years","chinDesc":"","attributes":[{}]}'
        +N']',
		'23-Jul-2023',
		NULL,
		'SYSTEM',
		'23-Jul-2023');

commit;