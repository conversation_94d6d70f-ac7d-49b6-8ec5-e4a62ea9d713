------------------------------------------------------------------------------------------
-- Mod. Date : 12 Sep 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL05_NBUW_11436
-- Mod. Desc : Build Residency Mapping & Validation Setup in Customer Tab
-------------------------------------------------------------------------------------------
-- Mod. Date : 14 Mar 2024
-- Mod. By   : (MITDC CD) <PERSON> Wang
-- Mod. ref  : IFP_24_RL02_NBUW_14146_I
-- Mod. Desc : 2024 Apr Release - Support WIOP3 in NB2.0
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'RESIDENCY_PLAN_CATEGORY';

--IFP_24_RL02_NBUW_14146 start
--insert into sys_config values(
--    'RESIDENCY_PLAN_CATEGORY',
--    '[
--         {
--             "planCode": [ "UH501","UH505","UH510", "UG501", "UG505","UG510","UJ501" ,"UK501","UK505","UK510"],
--             "planCate": "WIOP",
--             "cityGroup": [ "UH"]
--         },
--         {
--             "planCode": [ "UL101" ],
--             "planCate": "UL",
--             "cityGroup": ["UL"]
--         },
--         {
--             "planCode": [  "UN501" ],
--             "planCate": "UN",
--             "cityGroup": ["UN"]
--         },
--         {
--             "planCode": [ "*"],
--             "planCate": "General",
--             "cityGroup": ["FOREIGN","PRC","HKMC" ]
--         }
--     ]',
--    '05-Nov-2023',
--    'SYSTEM',
--    '05-Nov-2023'
--);
--IFP_24_RL02_NBUW_14146 end
insert into sys_config values(
    'RESIDENCY_PLAN_CATEGORY',
		CAST(N'[' AS nvarchar(max))
		 +N'{'
                     +N'"planCode": [ "UH501","UH505","UH510", "UG501", "UG505","UG510","UJ501" ,"UK501","UK505","UK510"],'
                     +N'"planCate": "WIOP",'
                     +N'"cityGroup": [ "UH"]'
                 +N'},'
                 +N'{'
                     +N'"planCode": [ "UL101" ],'
                     +N'"planCate": "UL",'
                     +N'"cityGroup": ["UL"]'
                 +N'},'
                 +N'{'
                     +N'"planCode": [  "UN501" ],'
                     +N'"planCate": "UN",'
                     +N'"cityGroup": ["UN"]'
                 +N'},'
                 +N'{'
                     +N'"planCode": [ "*"],'
                     +N'"planCate": "General",'
                     +N'"cityGroup": ["FOREIGN","PRC","HKMC" ]'
                 +N'}'
    +N']',
    '05-Nov-2023',
    'SYSTEM',
    '21-Apr-2024'
 );

commit;