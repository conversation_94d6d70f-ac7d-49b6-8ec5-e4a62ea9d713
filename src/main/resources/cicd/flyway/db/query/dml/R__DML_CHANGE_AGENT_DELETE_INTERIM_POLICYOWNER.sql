--------------------------------------------------------------------------------------------
-- Mod. Date : 03 Sep 2024
-- Mod. By   : (MITDC CD) <PERSON> Xiao
-- Mod. ref  : IFP_24_RL05_NBUW_22006
-- Mod. Desc : [Genesis & WIOP3] - Legacy Planning - Nov 2024 Release
-------------------------------------------------------------------------------------------
--  Mod. Date : 26 Nov 2024
--  Mod. By   : (MITDC CD) Parker Song
--  Mod. ref  : IFP_25_RL01_NBUW_29525
--  Mod. Desc : 2025 Jan Release - 2024 New Product - New Saving (NBUWS)
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'INTERIM_POLICYOWNER_IN_SCOPE_PRODUCT';


insert into sys_config values('INTERIM_POLICYOWNER_IN_SCOPE_PRODUCT',
	CAST(N'['AS nvarchar(max))
	+N'{"planCode":"PU500","schmCode":"WB012400","coverageClass":null}'
    +N',{"planCode":"PU500","schmCode":"WB032400","coverageClass":null}'
    +N',{"planCode":"PU500","schmCode":"WB052400","coverageClass":null}'
    +N',{"planCode":"PU500","schmCode":"WB102400","coverageClass":null}'
	+N',{"planCode":"PU500","schmCode":"WB152400","coverageClass":null}'
    +N',{"planCode":"UK501","schmCode":null,"coverageClass":null}'
    +N',{"planCode":"UK505","schmCode":null,"coverageClass":null}'
	+N',{"planCode":"UK510","schmCode":null,"coverageClass":null}'
    --IFP_25_RL01_NBUW_29525 added start
	+N',{"planCode":"PU500","schmCode":"WB012500","coverageClass":null}'
	+N',{"planCode":"PU500","schmCode":"WB022500","coverageClass":null}'
    +N',{"planCode":"PU500","schmCode":"WB032500","coverageClass":null}'
    +N',{"planCode":"PU500","schmCode":"WB052500","coverageClass":null}'
    --IFP_25_RL01_NBUW_29525 added end
    +N']','03-Nov-2024','SYSTEM','05-Jan-2025');

commit;
