------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'PREMIUM_OVERRIDE';

insert into list_of_value values('PREMIUM_OVERRIDE',N'[{"code":"A","engDesc":"Amount Override","chinDesc":"","attributes":[{"nbwbCode":"A","eposValue":""}]},{"code":"D","engDesc":"Amount Discount","chinDesc":"","attributes":[{"nbwbCode":"D","eposValue":""}]},{"code":"G","engDesc":"Age Discount","chinDesc":"","attributes":[{"nbwbCode":"G","eposValue":""}]},{"code":"R","engDesc":"Rate Discount","chinDesc":"","attributes":[{"nbwbCode":"R","eposValue":""}]},{"code":"P","engDesc":"Percentage Discount","chinDesc":"","attributes":[{"nbwbCode":"P","eposValue":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
