-------------------------------------------------------------------------------------------
 -- Mod. Date : 19 Apr 2023
 -- Mod. By   : (MITDC CD) <PERSON> Hu
 -- Mod. ref  : IFP_23_RL04_NBUW_7176
 -- Mod. Desc : 2023 Jul Release - Data Capture & Update - Plan and Coverage (ILAS)
--------------------------------------------------------------------------------------------
 -- Mod. Date : 21 Sep 2023
 -- Mod. By   : (MITDC CD) <PERSON>
 -- Mod. ref  : IFP_23_RL05_NBUW_9923
 -- Mod. Desc : 2023 Nov Release - Data Capture & Update - Fund [Fund Service Integration]
--------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'MI_PLUS_IN_SCOPE_PRODUCT';

insert into LIST_OF_VALUE
values(
        'MI_PLUS_IN_SCOPE_PRODUCT',
        CAST(N'[' AS nvarchar(max))
		    +N'{"code":"MS105","engDesc":"Manulife Investment Plus","chinDesc":"","attributes":[{}]}'
            --+N',{"code":"MSD01","engDesc":"Manulife Investment Plus 2","chinDesc":"","attributes":[{}]}'--230921 IFP_23_RL05_NBUW_9923
        +N']',
		'23-Jul-2023',
		NULL,
		'SYSTEM',
		'23-Jul-2023');

commit;