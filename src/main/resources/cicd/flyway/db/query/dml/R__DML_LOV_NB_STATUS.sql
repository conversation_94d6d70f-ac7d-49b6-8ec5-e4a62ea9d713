------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'NB_STATUS';

insert into list_of_value values('NB_STATUS',N'[{"code":"P","engDesc":"Pending","chinDesc":"","attributes":[{"nbwbCode":"P","eposCode":"P"}]}, {"code":"S","engDesc":"Subject to","chinDesc":"","attributes":[{"nbwbCode":"S","eposCode":"S"}]}, {"code":"A","engDesc":"Approve","chinDesc":"","attributes":[{"nbwbCode":"A","eposCode":"A"}]}, {"code":"R","engDesc":"Drop","chinDesc":"","attributes":[{"nbwbCode":"R","eposCode":"R"}]}, {"code":"D","engDesc":"Decline","chinDesc":"","attributes":[{"nbwbCode":"D","eposCode":"D"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
