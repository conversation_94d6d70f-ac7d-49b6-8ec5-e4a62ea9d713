-------------------------------------------------------------------------------------------
-- Mod. Date : 03 Mar 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_6958
-- Mod. Desc : Data Capture & Update - Coverage Information - Part 2 of 2
-------------------------------------------------------------------------------------------
-- Mod. Date : 08 Sep 2023
-- Mod. By   : (MITDC CD) Joe <PERSON>
-- Mod. ref  : IFP_23_RL05_NBUW_11322
-- Mod. Desc : Support Macau Supreme Rider - Nov 2023 Release
-------------------------------------------------------------------------------------------


delete from LIST_OF_VALUE where LIST_CODE = 'COVERAGE_SOS_CARD';

--20230908 IFP_23_RL05_NBUW_11322 modified start
/*
insert into LIST_OF_VALUE values(
    'COVERAGE_SOS_CARD',
    CAST(N'[{"code":"N","engDesc":"N","chinDesc":"","attributes":[],"additionalInfo":[{"productMapping":[{"convertCode":"Y","supportProductList":["*"]}]}]}'AS nvarchar(max))
    +N',{"code":"Y","engDesc":"Y","chinDesc":"","attributes":[],"additionalInfo":[{"productMapping":[{"convertCode":"N","supportProductList":["*"]}]}]}'
    +N',{"code":"2","engDesc":"Card 2 only","chinDesc":"","attributes":[],"additionalInfo":[{"productMapping":[{"convertCode":"6","supportProductList":["HS599","HH599","HQ599"]},{"convertCode":"8","supportProductList":["HO599","HR599"]}]}]}'
    +N',{"code":"6","engDesc":"Card 6 only","chinDesc":"","attributes":[],"additionalInfo":[{"productMapping":[{"convertCode":"2","supportProductList":["HS599","HH599","HQ599"]}]}]}'
    +N',{"code":"8","engDesc":"Card 8 only","chinDesc":"","attributes":[],"additionalInfo":[{"productMapping":[{"convertCode":"2","supportProductList":["HO599","HR599"]}]}]}'
    +N']'
    ,'23-APR-2023'
    ,NULL
    ,'SYSTEM'
    ,'23-APR-2023'
);
*/
insert into LIST_OF_VALUE values(
    'COVERAGE_SOS_CARD',
    CAST(N'['
    +N'{"code":"N","engDesc":"N","chinDesc":"","attributes":[],"additionalInfo":['
        +N'{"productMapping":['
            +N'{"convertCode":"Y","supportProductList":["*"],"covertCondition":{}}'
        +N']}'
    +N']}'AS nvarchar(max))
    +N',{"code":"Y","engDesc":"Y","chinDesc":"","attributes":[],"additionalInfo":['
        +N'{"productMapping":['
            +N'{"convertCode":"N","supportProductList":["*"],"covertCondition":{}}'
        +N']}'
    +N']}'
    +N',{"code":"2","engDesc":"Card 2 only","chinDesc":"","attributes":[],"additionalInfo":['
        +N'{"productMapping":['
            +N'{"convertCode":"6","supportProductList":["HS599","HH599","HQ599"],"covertCondition":{}}'
            +N',{"convertCode":"8","supportProductList":["HO599","HR599"],"covertCondition":{}}'
            +N',{"convertCode":"6","supportProductList":["HW599","HU599","HX599"],"covertCondition":{"conditionField":"residency","conditionValue":["HK0","17"],"conditionSymbol":"allow"}}'
            +N',{"convertCode":"5","supportProductList":["HW599","HX599","HU599"],"covertCondition":{"conditionField":"residency","conditionValue":["HK0","17"],"conditionSymbol":"block"}}'
        +N']}'
    +N']}'
    +N',{"code":"5","engDesc":"Card 5 only","chinDesc":"","attributes":[],"additionalInfo":['
        +N'{"productMapping":['
            +N'{"convertCode":"2","supportProductList":["HW599","HU599","HX599"],"covertCondition":{"conditionField":"residency","conditionValue":["HK0","17"],"conditionSymbol":"block"}}'
        +N']}'
    +N']}'
    +N',{"code":"6","engDesc":"Card 6 only","chinDesc":"","attributes":[],"additionalInfo":['
        +N'{"productMapping":['
            +N'{"convertCode":"2","supportProductList":["HS599","HH599","HQ599"],"covertCondition":{}}'
            +N',{"convertCode":"2","supportProductList":["HW599","HU599","HX599"],"covertCondition":{"conditionField":"residency","conditionValue":["HK0","17"],"conditionSymbol":"allow"}}'
        +N']}'
    +N']}'
    +N',{"code":"8","engDesc":"Card 8 only","chinDesc":"","attributes":[],"additionalInfo":['
       +N'{"productMapping":['
            +N'{"convertCode":"2","supportProductList":["HO599","HR599"],"covertCondition":{}}'
       +N']}'
    +N']}'
    +N']'
    ,'05-Nov-2023'
    ,NULL
    ,'SYSTEM'
    ,'05-Nov-2023'
);
--20230908 IFP_23_RL05_NBUW_11322 modified end
commit;