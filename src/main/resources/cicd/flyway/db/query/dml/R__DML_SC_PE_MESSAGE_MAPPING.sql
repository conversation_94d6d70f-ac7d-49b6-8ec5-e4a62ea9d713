------------------------------------------------------------------------------------------
-- Mod. Date : 08 Feb 2023
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_6947
-- Mod. Desc : PE validation for NBUWS (Part 1 of 2) from PER0002 ~ PER0016
-------------------------------------------------------------------------------------------
-- Mod. Date : 13 Apr 2023
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_6916
-- Mod. Desc : PE validation for NBUWS (Part 2 of 2) from PER0017 ~ PER0040
-------------------------------------------------------------------------------------------
 -- Mod. Date : 07 Sept 2023
 -- Mod. By   : (MITDC CD) Darnell Wu
 -- Mod. ref  : IFP_23_RL05_NBUW_11285
 -- Mod. Desc : 2023 Nov Release - Support HK VHIS Rider -- PER0042
--------------------------------------------------------------------------------------------
-- Mod. Date : 05 Mar 2024
-- Mod. By   : (MITDC CD) Jason Zhang
-- Mod. ref  : IFP_24_RL02_NBUW_14110_BE_J
-- Mod. Desc : 2024 Apr Release - 2024 New Product - Genesis (NBUWS)
-------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) Bean Wang
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'PE_MESSAGE_MAPPING';

insert into sys_config values(
    'PE_MESSAGE_MAPPING',
    CAST(N'[' AS nvarchar(max))
            +N'{"type": "E","code": "ERR00023","mappingCode": "PER0002","formattedMappingMessage": "{PLAN_CODE} - Issue age beyond {FIELD_NAME} ({VALUE_LIMIT})","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00009, ERRC0013","mappingCode": "PER0003","formattedMappingMessage": "{PRODUCT_ID} - Sex not supported","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00062","mappingCode": "PER0004","formattedMappingMessage": "{PLAN_CODE} - {CURRENCY} not allowed as a policy currency","oneToOne": true,"skip": false}'
            +N',{"type": null,"code": "ERR00038","mappingCode": null,"formattedMappingMessage": null,"oneToOne": false,"formattedMappingMessageClazz": "PE00038","skip": false}'
            +N',{'
            +N'    "type": "E"'
            +N'    ,"code": "ERR00059"'
            +N'    ,"mappingCode": "PER0006"'
            +N'    ,"formattedMappingMessage": "{PLAN_CODE} - Payor age beyond {FIELD_NAME} ({VALUE_LIMIT})"'
            +N'    ,"oneToOne": true'
            +N'    ,"skip": false'
            +N'    ,"replacementList": {'
            +N'        "FIELD_NAME": ['
            +N'            {'
            +N'                "value": "maximum Payor age",'
            +N'                "replacement": "maximum"'
            +N'            },'
            +N'            {'
            +N'                "value": "minimum Payor age",'
            +N'                "replacement": "minimum"'
            +N'            }'
            +N'        ]'
            +N'    }'
            +N' }'
            +N',{"type": "E","code": "ERR00048","mappingCode": "PER0007","formattedMappingMessage": "{PLAN_CODE} - Mutual exclusion on riders","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00056","mappingCode": "PER0008","formattedMappingMessage": "{PLAN_CODE} - Mutual exclusion on riders","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0305","mappingCode": "PER0009","formattedMappingMessage": "Duplicated rider code","oneToOne": true,"skip": false}'
            +N',{"type": null,"code": "ERR00039","mappingCode": null,"formattedMappingMessage": null,"oneToOne": false, "formattedMappingMessageClazz": "PE00039","skip": false}'
            +N',{"type": "E","code": "ERR00044","mappingCode": "PER0011","formattedMappingMessage": "{PLAN_CODE} - IPO not supported due to extra loading","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0010","mappingCode": "PER0012","formattedMappingMessage": "{PRODUCT_ID} - Payment mode not support","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00070","mappingCode": "PER0013","formattedMappingMessage": "{PLAN_CODE} - The age gap between insured and payor is greater than 45","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00025","mappingCode": "PER0014","formattedMappingMessage": "The total coverage of Accidental Coverage must be less than {CURRENCY} {MAXIMUM_FACE_AMOUNT}","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0171","mappingCode": "PER0016","formattedMappingMessage": "{PLAN_CODE} - Regular Top-up Premium beyond maximum {MAX} / {AGE}","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00049","mappingCode": "PER0017","formattedMappingMessage": "OB570 must be attached with HB599","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0017","mappingCode": "PER0018","formattedMappingMessage": "{SMOKING_STATUS} not supported for age 15 or below","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00069","mappingCode": "PER0019","formattedMappingMessage": "{PLAN_CODE} - Temp Flat Duration should be less than Premium Paying Term","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00026","mappingCode": "PER0020","formattedMappingMessage": "{BASIC_PLAN_CODE} - {PAYMENT_MODE} Premium beyond minimum {CURRENCY} {AMOUNT}","oneToOne": true,"skip": false}'
            +N',{"type": null,"code": "ERR00102","mappingCode": null,"formattedMappingMessage": null,"oneToOne": false, "formattedMappingMessageClazz": "PE00102","skip": false}'
            +N',{"type": "E","code": "ERRC0066","mappingCode": "PER0022","formattedMappingMessage": "{PLAN_CODE} - Payment Method PDF not supported","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00065","mappingCode": "PER0023","formattedMappingMessage": "Payment Method PDF only allow annual mode","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00031","mappingCode": "PER0024","formattedMappingMessage": "{BASIC_PLAN_CODE} - Instalment Period must be less than Premium Paying Term","oneToOne": true,"skip": false}'
            +N',{'
            +N'    "type": "E"'
            +N'    ,"code": "ERR00034"'
            +N'    ,"mappingCode": "PER0025"'
            +N'    ,"formattedMappingMessage": "{BASIC_PLAN_CODE} - Premium Prepayment Period must be 5 years or above"'
            +N'    ,"oneToOne": true'
            +N'    ,"skip": false'
            +N'    ,"excludeList": ['
            +N'        {"planCode": "FF300"}'
            +N'    ]'
            +N'}'
            +N',{"type": "E","code": "ERR00032","mappingCode": "PER0026","formattedMappingMessage": "{BASIC_PLAN_CODE} - Instalment Period must be less than Premium Prepayment Period","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00022","mappingCode": "PER0027","formattedMappingMessage": "{BASIC_PLAN_CODE} - PYD must later than Product Launch Date","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0170","mappingCode": "PER0029","formattedMappingMessage": "{PLAN_CODE} - Regular Top-up Premium beyond minimum {MIN}","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00043","mappingCode": "PER0030","formattedMappingMessage": "{PLAN_CODE} - IPO issue age beyond maximum {MAX_IPO_ISSUEAGE}","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00024_3","mappingCode": "PER0031","formattedMappingMessage": "{PLAN_CODE} - {FIELD_NAME} must be {CURRENCY} {MIN_FA_BAND1} - {MAX_FA_BAND1} or {CURRENCY} {MIN_FA_BAND2} - {MAX_FA_BAND2} or {CURRENCY} {MIN_FA_BAND3} - {MAX_FA_BAND3}","oneToOne": true,"skip": false}'
            +N',{"type": null,"code": "ERR00028","mappingCode": null,"formattedMappingMessage": null,"oneToOne": false, "formattedMappingMessageClazz": "PE00028","skip": false}'
            +N',{"type": "E","code": "ERR00060","mappingCode": "PER0033","formattedMappingMessage": "{PLAN_CODE} - Basic annualized premium beyond minimum {CURRENCY} {MIN_PREMIUM} for rider eligibility","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00064","mappingCode": "PER0034","formattedMappingMessage": "PDF not allowed when lifetime medical rider is selected","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00310","mappingCode": "PER0035","formattedMappingMessage": "{PRODUCT_ID} - Insured age 0 - 17 only allow Standard Non-Smoker","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0306","mappingCode": "PER0038","formattedMappingMessage": "{BASIC_PLAN_CODE} - Annuity Option Certain Period Payment only applicable to Age 100 tranche","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00050","mappingCode": "PER0040","formattedMappingMessage": "{PRODUCT_ID} - Risk Classification {SMOKING_STATUS} not supported","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERRC0219","mappingCode": "PER0042","formattedMappingMessage": "{SUB_PRODUCT_ID}/{OBJ_PRODUCT_ID} - Mutual exclusion on riders","oneToOne": true,"skip": false}'
            +N',{"type": null,"code": "ERRC0309","mappingCode": null,"formattedMappingMessage": null,"oneToOne": false, "formattedMappingMessageClazz": "PEC0309","skip": false}'
            +N',{"type": "E","code": "ERRC0314","mappingCode": "PER0045","formattedMappingMessage": "{BASIC_PLAN_CODE} - Premium Prepayment Period must be {MIN_YEAR} years or above","oneToOne": true,"skip": false}'
            -- IFP_24_RL04_NBUW_23637 added start
            +N',{"type": "E","code": "ERR00038","mappingCode": "PER0047","formattedMappingMessage": "{PLAN_NAME}: {FIELD_NAME} should be equal to or above {MIN}.","oneToOne": true,"skip": false}'
            +N',{"type": "E","code": "ERR00039","mappingCode": "PER0048","formattedMappingMessage": "{PLAN_NAME}: {FIELD_NAME} should be equal to or below {MAX}.","oneToOne": true,"skip": false}'
            -- IFP_24_RL04_NBUW_23637 added end
     +N']',
    '06-Apr-2023',
    'SYSTEM',
    '08-Sep-2024'
);

commit;
