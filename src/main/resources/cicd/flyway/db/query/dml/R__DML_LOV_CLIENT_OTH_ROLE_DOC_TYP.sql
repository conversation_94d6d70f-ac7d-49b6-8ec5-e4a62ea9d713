------------------------------------------------------------------------------------------
-- Mod. Date : 3 Mar 2020
-- Mod. By   : <PERSON>era EG
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 04 Jan 2023
-- Mod. By   : (MITDC CD) Eric <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_2347
-- Mod. Desc : 2023 Apr Release - Data Update - Basic Info - Successive Policyowner, Contingent Life Insured
-------------------------------------------------------------------------------------------

--IFP_23_RL02_NBUW_2347 modified start
delete from LIST_OF_VALUE where LIST_CODE = 'FMLY_DOC_TYP';

--insert into list_of_value values('FMLY_DOC_TYP',
--N'[{"code":"I","engDesc":"HK/Macau ID","chinDesc":"香港/澳門身份證","attributes":[{"nbwbCode":"I","eposCode":"H"}]}, {"code":"O","engDesc":"Others: (Please attach copy)","chinDesc":"其他：﹝請附上副本﹞","attributes":[{"nbwbCode":"O","eposCode":"O"}]}]'
--,'08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

delete from LIST_OF_VALUE where LIST_CODE = 'CLIENT_OTH_ROLE_DOC_TYP';

insert into LIST_OF_VALUE values(
    'CLIENT_OTH_ROLE_DOC_TYP',
    CAST(N'[{"code":"I","engDesc":"HK/Macau ID","chinDesc":"香港/澳門身份證","attributes":[{"nbwbCode":"I","eposCode":"H"}]}'AS nvarchar(max))
    +N', {"code":"O","engDesc":"Others","chinDesc":"其他","attributes":[{"nbwbCode":"O","eposCode":"O"}]}'
    +N']'
    ,'23-APR-2023'
    ,NULL
    ,'SYSTEM'
    ,'23-APR-2023'
);
--IFP_23_RL02_NBUW_2347 modified end

commit;
