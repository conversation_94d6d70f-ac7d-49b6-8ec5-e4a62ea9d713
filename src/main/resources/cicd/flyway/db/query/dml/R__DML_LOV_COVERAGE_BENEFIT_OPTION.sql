------------------------------------------------------------------------------------------
-- Mod. Date : 29 Sep 2022
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_22_RL05_NBUW_3653
-- Mod. Desc : COVERAGE BENEFIT OPTION
-------------------------------------------------------------------------------------------
-- Mod. Date : 31 Oct 2022
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_23_RL01_NBUW_3580
-- Mod. Desc : [DEV] App Summary – Coverage
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'COVERAGE_BENEFIT_OPTION';
--<< 221031 IFP_23_RL01_NBUW_3580 modified start
/*
insert into list_of_value values(
    'COVERAGE_BENEFIT_OPTION',
    N'[
        {"code":"N","engDesc":"WM118 - No","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":"N"}]},
		{"code":"H","engDesc":"WM118 - Health Max Program","chinDesc":"","attributes":[{"nbwbCode":"H","eposCode":"H"}]},
		{"code":"L","engDesc":"WM118 - Additional Life Coverage","chinDesc":"","attributes":[{"nbwbCode":"L","eposCode":"L"}]},
		{"code":"MW","engDesc":"CK/CM/CN/CQ/CR/CT - With CMA","chinDesc":"","attributes":[{"nbwbCode":"MW","eposCode":"MW"}]},
		{"code":"ME","engDesc":"CK/CM/CN/CQ/CR/CT - Exclude CMA","chinDesc":"","attributes":[{"nbwbCode":"ME","eposCode":"ME"}]},
		{"code":"MU","engDesc":"CK/CM/CN/CQ/CR/CT - Under Last CMA","chinDesc":"","attributes":[{"nbwbCode":"MU","eposCode":"MU"}]},
		{"code":"MF","engDesc":"CQ/CR/CT - Under First CMA","chinDesc":"","attributes":[{"nbwbCode":"MF","eposCode":"MF"}]}
    ]',
    '06-NOV-2022',
    NULL,
    'SYSTEM',
    '06-NOV-2022'
);
*/
-->> 221031 IFP_23_RL01_NBUW_3580 modified end
commit;

