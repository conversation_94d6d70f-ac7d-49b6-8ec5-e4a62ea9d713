------------------------------------------------------------------------------------------
-- Mod. Date : 08 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL04_NBUW_1486
-- Mod. Desc : [DEV] Data Analytic model in hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------

delete from NB_QUERY_CTRL where QUERY_NAME = 'GET_APP_INFO';
delete from NB_QUERY_CTRL where QUERY_NAME = 'GET_APP_AGENT_INFO';
delete from NB_QUERY_CTRL where QUERY_NAME = 'GET_APP_FORM';

insert into NB_QUERY_CTRL values(
    'GET_APP_INFO',
    N'SELECT * FROM app_info WHERE policy_key = :policyKey ',
    'Y'
);

insert into NB_QUERY_CTRL values(
    'GET_APP_AGENT_INFO',
    N'SELECT * FROM app_agent_info WHERE policy_key = :policyKey ',
    'Y'
);

insert into NB_QUERY_CTRL values(
    'GET_APP_FORM',
    N'SELECT * FROM app_form WHERE policy_key = :policyKey ',
    'Y'
);

commit;