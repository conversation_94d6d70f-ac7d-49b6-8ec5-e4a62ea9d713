------------------------------------------------------------------------------------------
-- Mod. Date : 05 Sep 2024
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_25_RL01_CRR_1017
-- Mod. Desc : CRR
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'NB_CRR_COMMAND_TABLE_CONFIG';

insert into sys_config values(
    'NB_CRR_COMMAND_TABLE_CONFIG',
     CAST(N'[' AS nvarchar(max))
            +N'{"tableName":"NB_ADMIN","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_AGENT","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_APP_INFO","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_BANCA","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_BENEFICIARY","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_COVERAGE","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_CPD","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_CRS","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_FAMILY_MEMBER","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_FATCA","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_FNA","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_FUND","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_INSURED","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_OCR","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_OWNER","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_PAYMENT_DATA","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_PAYMENT_OPTION","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_PAYOR","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_PE_RESULT","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_REMARK","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_RULE_RESULT","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_SUB_DOC","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_SUCCESSIVE_CLIENT","condition":"policy_no = ''%policy_no%''"}'
            +N',{"tableName":"NB_UW","condition":"policy_no = ''%policy_no%''"}'
    +N']',
    '06-Jan-2025',
    'SYSTEM',
    '06-Jan-2025'
);

commit;