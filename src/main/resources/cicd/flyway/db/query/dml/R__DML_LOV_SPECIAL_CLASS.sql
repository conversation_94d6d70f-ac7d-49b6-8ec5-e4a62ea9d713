------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'SPECIAL_CLASS';

insert into list_of_value values('SPECIAL_CLASS',N'[{"code":"10","engDesc":"Keyman Replacement","chinDesc":"","attributes":[{"nbwbCode":"10","eposValue":""}]},{"code":"11","engDesc":"Term conversion","chinDesc":"","attributes":[{"nbwbCode":"11","eposValue":""}]},{"code":"13","engDesc":"Plan change","chinDesc":"","attributes":[{"nbwbCode":"13","eposValue":""}]},{"code":"16","engDesc":"Premium override","chinDesc":"","attributes":[{"nbwbCode":"16","eposValue":""}]},{"code":"17","engDesc":"Special Quote","chinDesc":"","attributes":[{"nbwbCode":"17","eposValue":""}]},{"code":"46","engDesc":"Special marketing condition","chinDesc":"","attributes":[{"nbwbCode":"46","eposValue":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');


commit;
