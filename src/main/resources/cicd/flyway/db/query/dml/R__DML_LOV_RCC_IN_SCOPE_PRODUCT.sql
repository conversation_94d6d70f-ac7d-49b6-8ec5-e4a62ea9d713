------------------------------------------------------------------------------------------
-- Mod. Date : 08 Dec 2022
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_23_RL01_NBUW_4743
-- Mod. Desc : Data Update - Coverage (non-ILAS) (Plan Options) - Part 2
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Apr 2023
-- Mod. By   : (MITDC CD) Ivan <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_7612
-- Mod. Desc : Data Update - Coverage - Premium Financing, No RCC, UI update
-------------------------------------------------------------------------------------------

delete from list_of_value where LIST_CODE = 'RCC_IN_SCOPE_PRODUCT';
-- 20230420 IFP_23_RL04_NBUW_7612 start
-- insert into list_of_value values(
--     'RCC_IN_SCOPE_PRODUCT',
--     CAST(N'[' AS nvarchar(max))
--         +N'{"code":"AD065","engDesc":"ACCIDENTAL DEATH BENEFIT","chinDesc":"","attributes":[{}]}'
--      +N']',
--      '02-Jan-2023',
--      NULL,
--      'SYSTEM',
--      '02-Jan-2023'
-- );
-- 20230420 IFP_23_RL04_NBUW_7612 end

commit;
