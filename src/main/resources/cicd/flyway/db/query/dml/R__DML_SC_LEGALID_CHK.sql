------------------------------------------------------------------------------------------
-- Mod. Date : 01 Jul 2022
-- Mod. By   : (MITDC CD) Yale Liu
-- Mod. ref  : IFP_22_RL04_NBUW_1634
-- Mod. Desc : ID Holder Control for Medical & Accident Plan/Rider
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'PLAN_LEGALID_TYP_CHK';

insert into sys_config values(
    'PLAN_LEGALID_TYP_CHK',
    N'[
          {
            "planCode": "HH599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "HT599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "AL575",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "CD599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "AJ575",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "AK575",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "HQ599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          },
          {
            "planCode": "HB599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4"
          }
        ]',
    '24-Jul-2022',
    'SYSTEM',
    '24-Jul-2022'
);

commit;
