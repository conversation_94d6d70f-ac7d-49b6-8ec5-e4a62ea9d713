------------------------------------------------------------------------------------------
-- Mod. Date : 23 Mar 2022
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_22_RL03_NBUW_24
-- Mod. Desc : PYD Auto-Determination for May Release (Repricing)
-------------------------------------------------------------------------------------------
-- Mod. Date : 05 May 2022
-- Mod. By   : (MITDC CD) Bean Wang
-- Mod. ref  : IFP_22_RL04_NBUW_692
-- Mod. Desc : VHIS First Repricing Support
-------------------------------------------------------------------------------------------
-- Mod. Date : 07 Sep 2023
-- Mod. By   : (MITDC CD) Joan Zhao
-- Mod. ref  : IFP_23_RL05_NBUW_11326
-- Mod. Desc : 2023 Nov Release - Manulife Supreme VHIS Flexi Plan - Repricing
-------------------------------------------------------------------------------------------
-- Mod. Date : 21 Feb 2024
-- Mod. By   : (MITDC CD) Leo Li
-- Mod. ref  : IFP_24_RL02_NBUW_14126
-- Mod. Desc : 2024 Apr Release - 2024 VHIS First repricing
-------------------------------------------------------------------------------------------
-- Mod. Date : 18 Jun 2024
-- Mod. By   : (MITDC CD) Leo Li
-- Mod. ref  : IFP_24_RL03_NBUW_23562
-- Mod. Desc : Fix on the Cutoff Date of VHIS Repricing in Apr 2024 release
-------------------------------------------------------------------------------------------
-- Mod. Date : 10 Oct 2024
-- Mod. By   : (MITDC CD) Ives Wang
-- Mod. ref  : IFP_24_RL05_NBUW_13257
-- Mod. Desc : [2024R11] NBUWS - Medical Repricing
-------------------------------------------------------------------------------------------
-- Mod. Date : 07 Feb 2025
-- Mod. By   : (MITDC CD) Parker Song
-- Mod. ref  : IFP_25_RL02_NBUW_38211
-- Mod. Desc : 2025 VHIS First & VHIS Shelter repricing
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'REPRICING_SCOPE';

--<< 240222 IFP_24_RL02_NBUW_14126 modified start
--<< 230912 IFP_23_RL05_NBUW_11326 modified start
-- insert into sys_config values('REPRICING_SCOPE',N'[{"planCode": "HS599","schmCode": null,"coverageClass": null,"projPolEffDate": "2022-05-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-05-28","toInclude": true},{"type":"polEffDate","from": "2022-05-29","fromInclude": true,"to": "2022-06-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "A1N","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "A1P","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "A1Y","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "B1N","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "B1P","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "B1Y","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "C1N","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "C1P","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]},{"planCode": "HV599","schmCode": null,"coverageClass": "C1Y","projPolEffDate": "2022-09-28","enable": true,"rangeList": [{"type": "appSignDate","from": null,"fromInclude": false,"to": "2022-09-30","toInclude": true},{"type":"polEffDate","from": "2022-10-01","fromInclude": true,"to": "2022-10-28","toInclude": true}]}]','07-May-2022','SYSTEM','24-Jul-2022');
-- insert into sys_config values( 'REPRICING_SCOPE', N'[ { "planCode": "HS599", "schmCode": null, "coverageClass": null, "projPolEffDate": "2023-12-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2023-12-28", "toInclude": true }, { "type":"polEffDate", "from": "2024-01-01", "fromInclude": true, "to": "2024-01-29", "toInclude": false } ] } , { "planCode": "HV599", "schmCode": null, "coverageClass": "A1N", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "A1P", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "A1Y", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "B1N", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "B1P", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "B1Y", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "C1N", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "C1P", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] }, { "planCode": "HV599", "schmCode": null, "coverageClass": "C1Y", "projPolEffDate": "2022-09-28", "enable": true, "rangeList": [ { "type": "appSignDate", "from": null, "fromInclude": false, "to": "2022-09-30", "toInclude": true }, { "type":"polEffDate", "from": "2022-10-01", "fromInclude": true, "to": "2022-10-28", "toInclude": true } ] } ]', '07-May-2022', 'SYSTEM', '06-Nov-2023');
-->> 230912 IFP_23_RL05_NBUW_11326 modified end
insert into sys_config values(
    'REPRICING_SCOPE',
    CAST(N'[' AS nvarchar(max))
+N'        {'
+N'            "planCode": "HS599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
--<< 241010 IFP_24_RL05_NBUW_13257
--+N'            "projPolEffDate": "2023-12-28",'
+N'            "projPolEffDate": "2024-12-28",'
--<< 241010 IFP_24_RL05_NBUW_13257
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 241010 IFP_24_RL05_NBUW_13257
+N'                    "to": "2024-12-28",'
--+N'                    "to": "2023-12-28",'
--<< 241010 IFP_24_RL05_NBUW_13257
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 241010 IFP_24_RL05_NBUW_13257
--+N'                    "from": "2024-01-01",'
+N'                    "from": "2025-01-01",'
--<< 241010 IFP_24_RL05_NBUW_13257
+N'                    "fromInclude": true,'
--<< 241010 IFP_24_RL05_NBUW_13257
--+N'                    "to": "2024-01-29",'
--+N'                    "toInclude": false'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
--<< 241010 IFP_24_RL05_NBUW_13257
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "A1N",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "A1P",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "A1Y",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "B1N",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "B1P",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "B1Y",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "C1N",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "C1P",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        },'
+N'        {'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "C1Y",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'            "projPolEffDate": "2024-06-28",'
+N'            "projPolEffDate": "2025-06-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--<< 240618 IFP_24_RL03_NBUW_23562 modified start
--+N'                    "to": "2024-06-30",'
--+N'                    "to": "2024-06-28",'
+N'                    "to": "2025-06-28",'
-->> 240618 IFP_24_RL03_NBUW_23562 modified end
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "from": "2024-07-01",'
+N'                    "from": "2025-07-01",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "fromInclude": true,'
--<< 250207 IFP_25_RL02_NBUW_38211 modified start
--+N'                    "to": "2024-07-28",'
+N'                    "to": "2025-07-28",'
-->> 250207 IFP_25_RL02_NBUW_38211 modified end
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
--<< 250207 IFP_25_RL02_NBUW_38211 add start
+N'        ,{'
+N'            "planCode": "HV599",'
+N'            "schmCode": null,'
+N'            "coverageClass": "E1N",'
+N'            "projPolEffDate": "2025-06-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2025-06-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-07-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-07-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
-->> 250207 IFP_25_RL02_NBUW_38211 add end
--<< 241010 IFP_24_RL05_NBUW_13257
+N'        ,{'
+N'            "planCode": "HH599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HO599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HX599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HQ599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HR599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HW599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HU599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2024-12-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2024-12-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-01-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-01-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
--<< 250207 IFP_25_RL02_NBUW_38211 add start
+N'        ,{'
+N'            "planCode": "HP599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2025-06-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2025-06-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-07-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-07-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HB599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2025-06-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2025-06-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-07-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-07-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
+N'        ,{'
+N'            "planCode": "HN599",'
+N'            "schmCode": null,'
+N'            "coverageClass": null,'
+N'            "projPolEffDate": "2025-06-28",'
+N'            "enable": true,'
+N'            "rangeList": ['
+N'                {'
+N'                    "type": "appSignDate",'
+N'                    "from": null,'
+N'                    "fromInclude": false,'
+N'                    "to": "2025-06-28",'
+N'                    "toInclude": true'
+N'                },'
+N'                {'
+N'                    "type":"polEffDate",'
+N'                    "from": "2025-07-01",'
+N'                    "fromInclude": true,'
+N'                    "to": "2025-07-28",'
+N'                    "toInclude": true'
+N'                }'
+N'            ]'
+N'        }'
-->> 250207 IFP_25_RL02_NBUW_38211 add end
+N'    ]',
    '07-May-2022',
    'SYSTEM',
    '13-Apr-2025'
--<< 241010 IFP_24_RL05_NBUW_13257
);
-->> 240222 IFP_24_RL02_NBUW_14126 modified end
commit;
