------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 08 Aug 2022
-- Mod. By   : (MITDC CD) Ivan <PERSON>
-- Mod. ref  : IFP_22_RL05_NBUW_1785
-- Mod. Desc : Add new policyowner relationship - code is 29
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'OWNER_RELATIONSHIP_TO_INSURED';

insert into list_of_value values('OWNER_RELATIONSHIP_TO_INSURED',N'[{"code":"01","engDesc":"Aunt","chinDesc":" 姑/姨/嬸/舅母/伯母 ","attributes":[{"nbwbCode":"01","eposCode":"01"}]}, {"code":"03","engDesc":"Brother","chinDesc":"兄弟","attributes":[{"nbwbCode":"03","eposCode":"03"}]}, {"code":"02","engDesc":"Business Partner","chinDesc":"","attributes":[{"nbwbCode":"02","eposCode":"02"}]}, {"code":"04","engDesc":"Cousin","chinDesc":"堂/表兄弟姊妹","attributes":[{"nbwbCode":"04","eposCode":"04"}]}, {"code":"05","engDesc":"Daughter","chinDesc":"女兒","attributes":[{"nbwbCode":"05","eposCode":"05"}]}, {"code":"06","engDesc":"Daughter-In-Law","chinDesc":"兒媳","attributes":[{"nbwbCode":"06","eposCode":"06"}]}, {"code":"07","engDesc":"Employer","chinDesc":"","attributes":[{"nbwbCode":"07","eposCode":"07"}]}, {"code":"08","engDesc":"Father","chinDesc":"父親","attributes":[{"nbwbCode":"08","eposCode":"08"}]}, {"code":"09","engDesc":"Father-In-Law ","chinDesc":"配偶的父親","attributes":[{"nbwbCode":"09","eposCode":"09"}]}, {"code":"10","engDesc":"Fiance","chinDesc":"未婚夫","attributes":[{"nbwbCode":"10","eposCode":"10"}]}, {"code":"11","engDesc":"Fiancee","chinDesc":"未婚妻","attributes":[{"nbwbCode":"11","eposCode":"11"}]}, {"code":"12","engDesc":"Friend","chinDesc":"朋友","attributes":[{"nbwbCode":"12","eposCode":"12"}]}, {"code":"13","engDesc":"Granddaughter","chinDesc":"(外)孫女","attributes":[{"nbwbCode":"13","eposCode":"13"}]}, {"code":"14","engDesc":"Grandfather","chinDesc":"(外)祖父","attributes":[{"nbwbCode":"14","eposCode":"14"}]}, {"code":"15","engDesc":"Grandmother","chinDesc":"(外)祖母","attributes":[{"nbwbCode":"15","eposCode":"15"}]}, {"code":"16","engDesc":"Grandson","chinDesc":"(外)男孫","attributes":[{"nbwbCode":"16","eposCode":"16"}]}, {"code":"17","engDesc":"Husband","chinDesc":"丈夫","attributes":[{"nbwbCode":"17","eposCode":"17"}]}, {"code":"18","engDesc":"Mother","chinDesc":"母親","attributes":[{"nbwbCode":"18","eposCode":"18"}]}, {"code":"19","engDesc":"Mother-In-Law","chinDesc":"配偶的母親","attributes":[{"nbwbCode":"19","eposCode":"19"}]}, {"code":"20","engDesc":"Nephew","chinDesc":"姪/外甥(男)","attributes":[{"nbwbCode":"20","eposCode":"20"}]}, {"code":"21","engDesc":"Niece","chinDesc":"姪/外甥(女)","attributes":[{"nbwbCode":"21","eposCode":"21"}]}, {"code":"22","engDesc":"Others","chinDesc":"","attributes":[{"nbwbCode":"22","eposCode":"22"}]}, {"code":"23","engDesc":"Self","chinDesc":"本人","attributes":[{"nbwbCode":"23","eposCode":"23"}]}, {"code":"24","engDesc":"Sister","chinDesc":"姊妹","attributes":[{"nbwbCode":"24","eposCode":"24"}]}, {"code":"25","engDesc":"Son","chinDesc":"兒子","attributes":[{"nbwbCode":"25","eposCode":"25"}]}, {"code":"26","engDesc":"Son-In-Law","chinDesc":"女婿","attributes":[{"nbwbCode":"26","eposCode":"26"}]}, {"code":"27","engDesc":"Uncle","chinDesc":"叔/舅/伯/姑丈/姨丈","attributes":[{"nbwbCode":"27","eposCode":"27"}]}, {"code":"28","engDesc":"Wife","chinDesc":"妻子","attributes":[{"nbwbCode":"28","eposCode":"28"}]}, {"code":"29","engDesc":"Same-sex Married Partner","chinDesc":"同性已婚伴侶","attributes":[{"nbwbCode":"29","eposCode":"29"}]}]','08-NOV-2020',NULL,'SYSTEM','06-NOV-2022');

commit;
