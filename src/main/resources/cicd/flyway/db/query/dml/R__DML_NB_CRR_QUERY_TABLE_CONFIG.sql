------------------------------------------------------------------------------------------
-- Mod. Date : 05 Sep 2024
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_25_RL01_CRR_1017
-- Mod. Desc : CRR
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'NB_CRR_QUERY_TABLE_CONFIG';

insert into sys_config values(
    'NB_CRR_QUERY_TABLE_CONFIG',
     CAST(N'[' AS nvarchar(max))
            +N'{"tableName":"APP_AGENT_INFO","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_BANCA","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_CAMPAIGN","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_CLIENT_ROLE_INFO","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_CLIENT_ROLE_LINK","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_COVERAGE_BENEFIT","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_CPD","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_FORM","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_FUND","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_ILAS_QUEST","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_ILAS_RESULT","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_INFO","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_OCR","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_OTHER_ROLE_INFO","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_PAYMENT_INFO","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_PAYOUT_INFO","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_PREM_SCHDL","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_UW_QUEST","condition":"policy_key = ''%policy_key%''"}'
            +N',{"tableName":"APP_UW_RESULT","condition":"policy_key = ''%policy_key%''"}'
    +N']',
    '06-Jan-2025',
    'SYSTEM',
    '06-Jan-2025'
);

commit;