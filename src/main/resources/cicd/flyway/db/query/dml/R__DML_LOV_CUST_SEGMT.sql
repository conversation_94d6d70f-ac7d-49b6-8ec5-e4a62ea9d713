-------------------------------------------------------------------------------------------
-- Mod. Date : 23 Feb 2024
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_24_RL03_NBUW_14136_17961
-- Mod. Desc : 2024 Jul Release - Add new section of "Bank Reference" under Agent tab - Layout & Display only
-------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'CUST_SEGMT';

insert into list_of_value values(
    'CUST_SEGMT',
    CAST(N'[' AS nvarchar(max))
            +N'{"code": "TPC","engDesc": "CBG - TPC","chinDesc": "","attributes": [{}]}'
            +N',{"code": "TREASURES","engDesc": "CBG - Treasures","chinDesc": "","attributes": [{}]}'
            +N',{"code": "DBS","engDesc": "CBG - DBS","chinDesc": "","attributes": [{}]}'
            +N',{"code": "POSB","engDesc": "CBG - POSB","chinDesc": "","attributes": [{}]}'
            +N',{"code": "DIGITAL","engDesc": "CBG - Digital Platform","chinDesc": "","attributes": [{}]}'
            +N',{"code": "IBG","engDesc": "IBG","chinDesc": "","attributes": [{}]}'
            +N',{"code": "PB","engDesc": "PB","chinDesc": "","attributes": [{}]}'
    +N']',
    '24-Jul-2024',
    NULL,
    'SYSTEM',
    '24-Jul-2024'
);

commit;