-------------------------------------------------------------------------------------------
 -- Mod. Date : 10 Jan 2023
 -- Mod. By   : (MITDC CD) <PERSON><PERSON> Wu
 -- Mod. ref  : IFP_23_RL02_NBUW_4907
 -- Mod. Desc : 2023 Apr Release - Total Premium checking per Insured for GIO products - NBUWS valuation rule
--------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'GIO_PRODUCT';

--IFP_23_RL02_NBUW_4907 added start
insert into SYS_CONFIG
values
	 ('GIO_PRODUCT',
		'[
		    {"planCode":"PU500","schmCode":"WB011900"},
		    {"planCode":"PU500","schmCode":"WB051900"},
		    {"planCode":"PU500","schmCode":"WB101900"},
		    {"planCode":"PU500","schmCode":"WB021900"},
		    {"planCode":"PU500","schmCode":"WB012000"},
		    {"planCode":"PU500","schmCode":"WB052000"},
		    {"planCode":"PU500","schmCode":"WB102000"},
		    {"planCode":"PU500","schmCode":"WB022000"},
		    {"planCode":"PU500","schmCode":"WB051901"},
		    {"planCode":"PU500","schmCode":"WB101901"},
		    {"planCode":"PU500","schmCode":"WB052001"},
            {"planCode":"PU500","schmCode":"WB102001"},
            {"planCode":"PU500","schmCode":"WB152101"},
            {"planCode":"PU500","schmCode":"WB012101"},
            {"planCode":"PU500","schmCode":"WB012202"},
            {"planCode":"PU500","schmCode":"WB032202"},
            {"planCode":"PU500","schmCode":"WB052202"},
            {"planCode":"PU500","schmCode":"WB102202"},
            {"planCode":"PU500","schmCode":"WB012203"},
            {"planCode":"PU500","schmCode":"WB032203"},
            {"planCode":"PU500","schmCode":"WS012203"},
            {"planCode":"PU500","schmCode":"WS032203"},
            {"planCode":"PU500","schmCode":"WS102203"}
        ]',
		'23-Apr-2023',
		'SYSTEM',
		'23-Apr-2023');

--IFP_23_RL02_NBUW_4907 added end

commit;