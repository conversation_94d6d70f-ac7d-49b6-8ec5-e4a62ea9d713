------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 16 Feb 2022
-- Mod. By   : (MITDC CD) <PERSON> Pan
-- Mod. ref  : IFP_22_RL04_JMIFUM_249
-- Mod. Desc : 2021 Jul Release - MCV indicator change - NB
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'MCV_INDICATOR';

insert into list_of_value values(
    'MCV_INDICATOR',
    N'[
        {"code":"2","engDesc":"App","chinDesc":"","attributes":[{"nbwbCode":"2","eposCode":""}]},
        {"code":"3","engDesc":"Counter","chinDesc":"","attributes":[{"nbwbCode":"3","eposCode":""}]},
        {"code":"4","engDesc":"Outstanding","chinDesc":"","attributes":[{"nbwbCode":"4","eposCode":""}]},
        {"code":"5","engDesc":"Exempted","chinDesc":"","attributes":[{"nbwbCode":"5","eposCode":""}]},
        {"code":"6","engDesc":"ePOS","chinDesc":"","attributes":[{"nbwbCode":"6","eposCode":""}]}
    ]',
    '08-NOV-2020',
    NULL,
    'SYSTEM',
    '03-APR-2022'
);

commit;
