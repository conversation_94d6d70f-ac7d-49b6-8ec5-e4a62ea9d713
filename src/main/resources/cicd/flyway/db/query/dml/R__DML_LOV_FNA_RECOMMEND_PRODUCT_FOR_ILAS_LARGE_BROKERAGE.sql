------------------------------------------------------------------------------------------
-- Mod. Date : 16 Sep 2024
-- Mod. By   : Cathy <PERSON>
-- Mod. ref  : IFP_25_RL01_NBUW_28210
-- Mod. Desc : [ePOS Brokerage - NB] Adding brokerage options in FNA Q5 in NB2.0
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'FNA_RECOMMEND_PRODUCT_FOR_ILAS_LARGE_BROKERAGE';

insert into list_of_value values(
    'FNA_RECOMMEND_PRODUCT_FOR_ILAS_LARGE_BROKERAGE',
    N'[
        {"code":"OTHERS","engDesc":"OTHERS - OTHERS","chinDesc":"","attributes":[{"riderType": "","channelType": "LBR"}]}
       ,{"code": "NOALT","engDesc": "NOALT - No any other alternatives for introduced","chinDesc": "","attributes":[{"riderType": "","channelType": "LBR"}]}
    ]',
    '05-Jan-2025',
    NULL,
    'SYSTEM',
    '05-Jan-2025'
);
commit;
