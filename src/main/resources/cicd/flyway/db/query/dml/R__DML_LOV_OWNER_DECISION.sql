------------------------------------------------------------------------------------------
-- Mod. Date : 17 Nov 2021
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_22_RL01_SB_NB_23249
-- Mod. Desc : Policy Replacement
-------------------------------------------------------------------------------------------
-- Mod. Date : 17 SEP 2023
-- Mod. By   : <PERSON><PERSON><PERSON>ang
-- Mod. ref  : IFP_23_RL05_NBUW_1612
-- Mod. Desc : Policy Replacement
-------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'OWNER_DECISION';

-- IFP_23_RL05_NBUW_1612 modified start
--insert into list_of_value values('OWNER_DECISION',N'[ { "code": "A", "engDesc": "Make Change & PR", "chinDesc": "", "attributes": [ { "nbwbCode": "A", "eposCode": "A" } ] }, { "code": "B", "engDesc": "Make Change & Not PR", "chinDesc": "", "attributes": [ { "nbwbCode": "B", "eposCode": "B" } ] }, { "code": "C", "engDesc": "Not Make Change", "chinDesc": "", "attributes": [ { "nbwbCode": "C", "eposCode": "C" } ] } ]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');
-- IFP_23_RL05_NBUW_1612 modified end

-- IFP_23_RL05_NBUW_1612 added start
insert into list_of_value values('OWNER_DECISION',N'[ { "code": "A", "engDesc": "Has/intends/not yet to change", "chinDesc": "", "attributes": [ { "nbwbCode": "A", "eposCode": "A" } ] }, { "code": "B", "engDesc": "Has/intends to but NOT using", "chinDesc": "", "attributes": [ { "nbwbCode": "B", "eposCode": "B" } ] }, { "code": "C", "engDesc": "Has not/no intention to change", "chinDesc": "", "attributes": [ { "nbwbCode": "C", "eposCode": "C" } ] } ]','22-SEP-2023',NULL,'SYSTEM','22-SEP-2023');
-- IFP_23_RL05_NBUW_1612 added end

commit;
