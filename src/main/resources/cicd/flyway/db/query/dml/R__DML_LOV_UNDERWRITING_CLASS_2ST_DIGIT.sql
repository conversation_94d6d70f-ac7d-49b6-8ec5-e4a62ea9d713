------------------------------------------------------------------------------------------
-- Mod. Date : 23 Nov 2021
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL01_SB_NB_22894
-- Mod. Desc : [DEV-FE] App Data Summary in NBUWS
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'UNDERWRITING_CLASS_2ST_DIGIT';

insert into list_of_value values('UNDERWRITING_CLASS_2ST_DIGIT',N'[{"code":"1","engDesc":"Standard","chinDesc":"","attributes":[{}]},{"code":"2","engDesc":"Medical Extra","chinDesc":"","attributes":[{}]},{"code":"3","engDesc":"Occupational Extra","chinDesc":"","attributes":[{}]},{"code":"4","engDesc":"Residential Extra or Avocation Extra","chinDesc":"","attributes":[{}]},{"code":"5","engDesc":"Drop","chinDesc":"","attributes":[{}]},{"code":"7","engDesc":"Decline","chinDesc":"","attributes":[{}]},{"code":"8","engDesc":"Exclusion on DI","chinDesc":"","attributes":[{}]},{"code":"9","engDesc":"Restriction of Benefit Period/ Waiting Period on DI","chinDesc":"","attributes":[{}]}]','23-NOV-2021',NULL,'SYSTEM','23-NOV-2021');

commit;
