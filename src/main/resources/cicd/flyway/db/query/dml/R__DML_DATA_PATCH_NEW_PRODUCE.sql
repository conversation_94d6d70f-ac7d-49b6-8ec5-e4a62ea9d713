-------------------------------------------------------------------------------------------
-- Mod. Date : 12 Oct 2022
-- Mod. By   : (MITDC CD) Cathy Zhao
-- Mod. ref  : IFP_22_RL05_NBUW_3907
-- Mod. Desc : 2022 Nov Release - Production - Data patching after each release
--------------------------------------------------------------------------------------------
 -- Mod. Date : 06 Jun 2023
 -- Mod. By   : (MITDC CD) Bruce Hu
 -- Mod. ref  : IFP_23_RL04_NBUW_4901
 -- Mod. Desc : 2023 Jul Release - Support rest of the Traditional Products in NBUWS
--------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'DATA_PATCH_NEW_PRODUCTS';

--IFP_22_RL05_NBUW_3907 start
--IFP_23_RL04_NBUW_4901 start
--    insert into SYS_CONFIG values(
--        'DATA_PATCH_NEW_PRODUCTS',
--        N'[
--            {
--           		"planCodeWithoutTrancheID": "TP510,TP520",
--           		"planCode": "PU500,RK500,RQ500",
--           		"trancheIDForPU500":"QB052000,QB082000,QB122000,QB152000,QE152000,QB052001,QB152001,QB052002,QB152002,QB082001,QB122001,QB082002,QB122002,WE012100,WE052100,WE102100,WE202100,WE252100,WE012101,WE052101,WE102101,WE202101,WE252101",
--           		"trancheIDForRK500":"RC051801,RC051802,RC151802,RC151801,RC101802,RC101801,RC201801,RC201802,RC651801,RC651802",
--           		"trancheIDForRQ500":"RC051910,RC101910,RC051911,RC101911,RC051912,RC101912,RC051913,RC101913,RC051914,RC101914",
--           		"enabled": true
--            }
--        ]',
--        NULL,
--        'SYSTEM',
--        '12-10-2022');
insert into SYS_CONFIG values(
        'DATA_PATCH_NEW_PRODUCTS',
        N'[
            {
           		"planCodeWithoutTrancheID": "DS065,FF300",
           		"planCode": "PU500",
           		"trancheIDForPU500":"WB012202,WB032202,WB052202,WB102202,WB032203,WB012203,WS032203,WS102203,WS012203,WB012000,WB052000,WB102000,WB052001,WB102001,WB152101,WB012101",
           		"enabled": true
            }
        ]',
        NULL,
        'SYSTEM',
        '23-Jul-2023');
--IFP_23_RL04_NBUW_4901 end
--IFP_22_RL05_NBUW_3907 end
commit;

