-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL05_NBUW_7642
-- Mod. Desc : 2023 Nov Release - Data Capture & Update - UL/HNW (Part 2)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'RATE_LOCK_YR';

insert into list_of_value values(
    'RATE_LOCK_YR',
    CAST(N'[' AS nvarchar(max))
            +N'{"code": "1","engDesc": "1","chinDesc": "","attributes": [{}]}'
            +N',{"code": "2","engDesc": "2","chinDesc": "","attributes": [{}]}'
            +N',{"code": "3","engDesc": "3","chinDesc": "","attributes": [{}]}'
            +N',{"code": "4","engDesc": "4","chinDesc": "","attributes": [{}]}'
            +N',{"code": "5","engDesc": "5","chinDesc": "","attributes": [{}]}'
    +N']',
    '05-Nov-2023',
    NULL,
    'SYSTEM',
    '05-Nov-2023'
);

commit;