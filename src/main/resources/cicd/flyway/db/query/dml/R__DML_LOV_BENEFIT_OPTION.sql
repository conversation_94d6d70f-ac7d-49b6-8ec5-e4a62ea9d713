------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'BENEFIT_OPTION';

insert into list_of_value values('BENEFIT_OPTION',N'[{"code":"H","engDesc":"WM118-Health Max Program","chinDesc":"","attributes":[{"nbwbCode":"H","eposValue":""}]},{"code":"L","engDesc":"WM118-Additional Life Coverage","chinDesc":"","attributes":[{"nbwbCode":"L","eposValue":""}]},{"code":"N","engDesc":"WM118-No","chinDesc":"","attributes":[{"nbwbCode":"N","eposValue":""}]},{"code":"MW","engDesc":"CK/CM/CN/CQ/CR/CT-With CMA","chinDesc":"","attributes":[{"nbwbCode":"MW","eposValue":""}]},{"code":"ME","engDesc":"CK/CM/CN/CQ/CR/CT-Exclude CMA","chinDesc":"","attributes":[{"nbwbCode":"ME","eposValue":""}]},{"code":"MU","engDesc":"CK/CM/CN/CQ/CR/CT-Under Last CMA","chinDesc":"","attributes":[{"nbwbCode":"MU","eposValue":""}]},{"code":"MF","engDesc":"CQ/CR/CT-Under First CMA","chinDesc":"","attributes":[{"nbwbCode":"MF","eposValue":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
