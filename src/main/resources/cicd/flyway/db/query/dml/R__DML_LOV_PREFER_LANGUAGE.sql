------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 18 Jan 2021
-- Mod. By   : Meera EG
-- Mod. ref  : IFP_21_RL02_SB_NB_14155
-- Mod. Desc : Support new LOV English + SC provision
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'PREFER_LANGUAGE';
--IFP_21_RL02_SB_NB_14155 start
--insert into list_of_value values('PREFER_LANGUAGE',N'[{"code":"E","engDesc":"English","chinDesc":"繁體中文","attributes":[{"nbwbCode":"Chinese Provision = N","eposCode":""}]}, {"code":"T","engDesc":"Traditional Chinese","chinDesc":"英文","attributes":[{"nbwbCode":"Chinese Provision = Y and SC Provision = N","eposCode":""}]}, {"code":"S","engDesc":"Simplified Chinese","chinDesc":"簡體中文","attributes":[{"nbwbCode":"Chinese Provision = Y and SC Provision = Y","eposCode":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

insert into list_of_value values('PREFER_LANGUAGE',N'[{"code":"E","engDesc":"English","chinDesc":"繁體中文","attributes":[{"nbwbCode":"Chinese Provision = N and SC Provision = N","eposCode":""}]}, {"code":"T","engDesc":"Traditional Chinese","chinDesc":"英文","attributes":[{"nbwbCode":"Chinese Provision = Y and SC Provision = N","eposCode":""}]}, {"code":"S","engDesc":"Simplified Chinese","chinDesc":"簡體中文","attributes":[{"nbwbCode":"Chinese Provision = N and SC Provision = Y","eposCode":""}]}, {"code":"C","engDesc":"Chinese","chinDesc":"中文","attributes":[{"nbwbCode":"Chinese Provision = Y and SC Provision = Y","eposCode":""}]}]','08-NOV-2020',NULL,'SYSTEM','07-MAR-2021');

--IFP_21_RL02_SB_NB_14155 end

commit;
