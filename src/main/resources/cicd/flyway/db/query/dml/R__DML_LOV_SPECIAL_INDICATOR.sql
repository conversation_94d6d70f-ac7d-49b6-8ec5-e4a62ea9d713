------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'SPECIAL_INDICATOR';

insert into list_of_value values('SPECIAL_INDICATOR',N'[{"code":"O","engDesc":"OD (for Premium)","chinDesc":"","attributes":[{"nbwbCode":"O","eposValue":""}]},{"code":"D","engDesc":"OD (Not for Premium)","chinDesc":"","attributes":[{"nbwbCode":"D","eposValue":""}]},{"code":"W","engDesc":"Special Sum Assured","chinDesc":"","attributes":[{"nbwbCode":"W","eposValue":""}]},{"code":"L","engDesc":"LV-Lien Clause","chinDesc":"","attributes":[{"nbwbCode":"L","eposValue":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
