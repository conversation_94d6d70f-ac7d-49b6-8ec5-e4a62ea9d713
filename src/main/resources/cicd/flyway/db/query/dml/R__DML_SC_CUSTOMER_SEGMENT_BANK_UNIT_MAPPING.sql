------------------------------------------------------------------------------------------
-- Mod. Date : 13 Mar 2024
-- Mod. By   : (MITDC CD) <PERSON><PERSON>
-- Mod. ref  : IFP_24_RL03_NBUW_15934_18051
-- Mod. Desc : "Bank Reference" under Agent tab - Validations
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'CUSTOMER_SEGMENT_BANK_UNIT_MAPPING';

insert into sys_config values(
    'CUSTOMER_SEGMENT_BANK_UNIT_MAPPING',
    CAST(N'[' AS nvarchar(max))
            -- white list start
            +N'{"customerSegment": "DIGITAL", "bankUnitList": "D6099", "allow": true}'
            +N',{"customerSegment": "IBG", "bankUnitList": "D6638|D6024", "allow": true}'
            -- means branchCode <> 826B0 && customerSegment = PB, bankUnit value must be one of bankUnitList
            +N',{"customerSegment": "PB", "branchCodeList": "826B0", "branchCodeInclude": false, "bankUnitList": "D6607|D6943", "allow": true}'
            +N',{"customerSegment": "PB", "branchCodeList": "826B0", "branchCodeInclude": true, "bankUnitList": "*", "allow": true}'
            +N',{"customerSegment": "TPC", "bankUnitList": "D6943", "allow": true}'
            -- white list end
            -- black list start
            +N',{"customerSegment": "TREASURES", "bankUnitList": "D6638|D6024", "allow": false}'
            +N',{"customerSegment": "DBS", "bankUnitList": "D6638|D6024", "allow": false}'
            +N',{"customerSegment": null, "bankUnitList": "D6638|D6024", "allow": false}'
            +N',{"customerSegment": "POSB", "bankUnitList": "*", "allow": false}'
            -- black list end
    +N']',
    '21-Jul-2024',
    'SYSTEM',
    '21-Jul-2024'
);

commit;
