------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'INSURANCE_TYPE_EPI';

insert into list_of_value values('INSURANCE_TYPE_EPI',N'[{"code":"LIFE","engDesc":"Life","chinDesc":"人壽保險","attributes":[{"nbwbCode":"N/A","eposCode":"LIFE"}]}, {"code":"CAB","engDesc":"Extra Pay Dread Disease","chinDesc":"額外危疾保障","attributes":[{"nbwbCode":"N/A","eposCode":"CAB"}]}, {"code":"MDB","engDesc":"Advanced Pay Dread Disease","chinDesc":"預支危疾保障","attributes":[{"nbwbCode":"N/A","eposCode":"MDB"}]}, {"code":"ACB","engDesc":"Accident Indemnity","chinDesc":"意外賠償","attributes":[{"nbwbCode":"N/A","eposCode":"ACB"}]}, {"code":"ADB","engDesc":"Accident Death","chinDesc":"意外死亡","attributes":[{"nbwbCode":"N/A","eposCode":"ADB"}]}, {"code":"DI","engDesc":"Disability Income","chinDesc":"傷殘入息","attributes":[{"nbwbCode":"N/A","eposCode":"DI"}]}, {"code":"HI","engDesc":"Hospital Cash/Income","chinDesc":"住院現金／入息","attributes":[{"nbwbCode":"N/A","eposCode":"HI"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
