------------------------------------------------------------------------------------------
-- Mod. Date    : 07 April 2022
-- Mod. By        : (MITDC CD) Auter <PERSON>
-- Mod. ref        : IFP_22_RL03_NBUW_19
-- Mod. Desc    : Add messages in NBUWS Remarks due to PYD adjustment (1. Age Change 2. Re-pricing)
------------------------------------------------------------------------------------------
-- Mod. Date : 10 Mar 2023
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_23_RL02_NBUW_6098_3933
-- Mod. Desc : Data Capture & Update - Basic Info
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'REMARK';

-- IFP_23_RL02_NBUW_6098_3933 start
--insert into list_of_value values(
--    'REMARK',
--    N'[
--            {
--                "code": "1",
--                "engDesc":  "Insured’s age is adjusted due to age change. PYD moved to last date before age change.",
--                "chinDesc": "",
--                "attributes": [
--                    {
--                        "type": "PYD",
--                        "category": "overview",
--                        "insuredAgeChangedIndicator": "Y",
--                        "inRepricingScopeIndicator": "N"
--                    }
--                 ]
--            },
--            {
--                "code":"2",
--                "engDesc":"Insured’s age is adjusted due to repricing. PYD moved to last date before repricing.",
--                "chinDesc":"",
--                "attributes":[
--                    {
--                        "type": "PYD",
--                        "category": "overview",
--                        "insuredAgeChangedIndicator": "N",
--                        "inRepricingScopeIndicator": "Y"
--                    }
--                 ]
--            },
--            {
--                "code":"3",
--                "engDesc":"Insured’s age is adjusted due to age change AND repricing. PYD moved to last date before age change and repricing, whichever is earlier.",
--                "chinDesc":"",
--                "attributes":[
--                    {
--                        "type": "PYD",
--                        "category": "overview",
--                         "insuredAgeChangedIndicator": "Y",
--                         "inRepricingScopeIndicator": "Y"
--                    }
--                ]
--            }
--    ]',
--    '07-MAY-2022',
--    NULL,
--    'SYSTEM',
--    '07-MAY-2022'
--);

insert into list_of_value values(
    'REMARK',
    CAST(N'['AS nvarchar(max))
         +N'{'
         +N'    "code": "1",'
         +N'    "engDesc":  "Insured’s age is adjusted due to age change. PYD moved to last date before age change.",'
         +N'    "chinDesc": "",'
         +N'    "attributes": ['
         +N'        {'
         +N'            "type": "PYD",'
         +N'            "category": "overview",'
         +N'            "insuredAgeChangedIndicator": "Y",'
         +N'            "inRepricingScopeIndicator": "N"'
         +N'        }'
         +N'     ]'
         +N'},'
         +N'{'
         +N'    "code":"2",'
         +N'    "engDesc":"Insured’s age is adjusted due to repricing. PYD moved to last date before repricing.",'
         +N'    "chinDesc":"",'
         +N'    "attributes":['
         +N'        {'
         +N'            "type": "PYD",'
         +N'            "category": "overview",'
         +N'            "insuredAgeChangedIndicator": "N",'
         +N'            "inRepricingScopeIndicator": "Y"'
         +N'        }'
         +N'     ]'
         +N'},'
         +N'{'
         +N'    "code":"3",'
         +N'    "engDesc":"Insured’s age is adjusted due to age change AND repricing. PYD moved to last date before age change and repricing, whichever is earlier.",'
         +N'    "chinDesc":"",'
         +N'    "attributes":['
         +N'        {'
         +N'            "type": "PYD",'
         +N'            "category": "overview",'
         +N'             "insuredAgeChangedIndicator": "Y",'
         +N'             "inRepricingScopeIndicator": "Y"'
         +N'        }'
         +N'    ]'
         +N'},'
         +N'{'
         +N'    "code": "4",'
         +N'    "engDesc":  "Insured’s age is adjusted due to age change. PYD moved to last date before age change.",'
         +N'    "chinDesc": "",'
         +N'    "attributes": ['
         +N'        {'
         +N'            "type": "PYD",'
         +N'            "category": "planInfo",'
         +N'            "insuredAgeChangedIndicator": "Y",'
         +N'            "inRepricingScopeIndicator": "N"'
         +N'        }'
         +N'     ]'
         +N'},'
         +N'{'
         +N'    "code":"5",'
         +N'    "engDesc":"Insured’s age is adjusted due to repricing. PYD moved to last date before repricing.",'
         +N'    "chinDesc":"",'
         +N'    "attributes":['
         +N'        {'
         +N'            "type": "PYD",'
         +N'            "category": "planInfo",'
         +N'            "insuredAgeChangedIndicator": "N",'
         +N'            "inRepricingScopeIndicator": "Y"'
         +N'        }'
         +N'     ]'
         +N'},'
         +N'{'
         +N'    "code":"6",'
         +N'    "engDesc":"Insured’s age is adjusted due to age change AND repricing. PYD moved to last date before age change and repricing, whichever is earlier.",'
         +N'    "chinDesc":"",'
         +N'    "attributes":['
         +N'        {'
         +N'            "type": "PYD",'
         +N'            "category": "planInfo",'
         +N'            "insuredAgeChangedIndicator": "Y",'
         +N'            "inRepricingScopeIndicator": "Y"'
         +N'        }'
         +N'    ]'
         +N'}'
    +N']',
    '26-APR-2023',
    NULL,
    'SYSTEM',
    '26-APR-2023'
);

commit;
--IFP_23_RL02_NBUW_6098_3933 end