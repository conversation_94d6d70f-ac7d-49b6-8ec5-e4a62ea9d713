-------------------------------------------------------------------------------------------
 -- Mod. Date : 24 Oct 2024
 -- Mod. By   : (MITDC CD) <PERSON>
 -- Mod. ref  : IFP_25_RL01_NBUW_30056
 -- Mod. Desc : Support Quick App in NBUWS Currency
-------------------------------------------------------------------------------------------

delete from SYS_CONFIG where CONFIG_CODE = 'CHK_FACE_AMOUNT_LIMIT';

insert into sys_config values(
    'CHK_FACE_AMOUNT_LIMIT',
    CAST(N'[' AS nvarchar(max))
		+N'{"planCode":"MT110","schmCode":"*","currency": "02","upperPremLimit": "500000","appType":"Q","planDesc":"Apollo Protection Linked Plan","upperPremLimitDesc":"USD500K or HKD4M"}'
		+N',{"planCode":"MT110","schmCode":"*","currency": "04","upperPremLimit": "4000000","appType":"Q","planDesc":"Apollo Protection Linked Plan","upperPremLimitDesc":"USD500K or HKD4M"}'
        +N',{"planCode":"MT115","schmCode":"*","currency": "02","upperPremLimit": "500000","appType":"Q","planDesc":"Apollo Protection Linked Plan","upperPremLimitDesc":"USD500K or HKD4M"}'
        +N',{"planCode":"MT115","schmCode":"*","currency": "04","upperPremLimit": "4000000","appType":"Q","planDesc":"Apollo Protection Linked Plan","upperPremLimitDesc":"USD500K or HKD4M"}'
        +N',{"planCode":"MT120","schmCode":"*","currency": "02","upperPremLimit": "500000","appType":"Q","planDesc":"Apollo Protection Linked Plan","upperPremLimitDesc":"USD500K or HKD4M"}'
        +N',{"planCode":"MT120","schmCode":"*","currency": "04","upperPremLimit": "4000000","appType":"Q","planDesc":"Apollo Protection Linked Plan","upperPremLimitDesc":"USD500K or HKD4M"}'
		+N',{"planCode":"MV510","schmCode":"*","currency": "02","upperPremLimit": "250000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
		+N',{"planCode":"MV510","schmCode":"*","currency": "04","upperPremLimit": "2000000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
        +N',{"planCode":"MV520","schmCode":"*","currency": "02","upperPremLimit": "250000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
        +N',{"planCode":"MV520","schmCode":"*","currency": "04","upperPremLimit": "2000000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
        +N',{"planCode":"MV525","schmCode":"*","currency": "02","upperPremLimit": "250000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
        +N',{"planCode":"MV525","schmCode":"*","currency": "04","upperPremLimit": "2000000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
        +N',{"planCode":"MV530","schmCode":"*","currency": "02","upperPremLimit": "250000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
        +N',{"planCode":"MV530","schmCode":"*","currency": "04","upperPremLimit": "2000000","appType":"Q","planDesc":"Alpha Regular Investor","upperPremLimitDesc":"USD250K or HKD2M"}'
    +N']',
    '03-Nov-2024',
	'SYSTEM',
	'05-Jan-2025');

commit;