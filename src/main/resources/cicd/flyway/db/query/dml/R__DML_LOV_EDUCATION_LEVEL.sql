------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'EDUCATION_LEVEL';

insert into list_of_value values('EDUCATION_LEVEL',N'[{"code":"A","engDesc":"Primary school or below","chinDesc":"小學或以下","attributes":[{"nbwbCode":"A","eposCode":"A"}]}, {"code":"B","engDesc":"Secondary School","chinDesc":"中學","attributes":[{"nbwbCode":"B","eposCode":"B"}]}, {"code":"C","engDesc":"Post-secondary/College","chinDesc":"專科／專上學院","attributes":[{"nbwbCode":"C","eposCode":"C"}]}, {"code":"D","engDesc":"University or above","chinDesc":"大學程度或以上","attributes":[{"nbwbCode":"D","eposCode":"D"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
