------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 11 Jul 2022
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_22_RL04_NBUW_1590
-- Mod. Desc : "Save" Behavior in NBUWS
-------------------------------------------------------------------------------------------
-- Mod. Date : 21 Nov 2022
-- Mod. By   : Dante Wang
-- Mod. ref  : IFP_23_RL01_NBUW_3600
-- Mod. Desc : Auto-Determination - discard App Type
-------------------------------------------------------------------------------------------
-- Mod. Date : 28 Feb 2023
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_23_RL02_NBUW_6098_3933
-- Mod. Desc : Data Capture & Update - Basic Info
-------------------------------------------------------------------------------------------
-- Mod. Date : 24 Apr 2024
-- Mod. By   : (MITDC CD) Renli Niu
-- Mod. ref  : IFP_24_RL03_NBUW_20341
-- Mod. Desc : Support upstream app type and smoker code for SPX in NB2.0
-------------------------------------------------------------------------------------------
 -- Mod. Date : 24 Oct 2024
 -- Mod. By   : (MITDC CD) Jason Zhang
 -- Mod. ref  : IFP_25_RL01_NBUW_30056
 -- Mod. Desc : Support Quick App in NBUWS Currency
 --------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'APP_TYPE';
delete from LIST_OF_VALUE where LIST_CODE = 'UPSTREAM_APP_TYPE';

--IFP_23_RL02_NBUW_6098_3933 start
--insert into list_of_value values(
--    'APP_TYPE',
--    N'[
--        {"code":"","engDesc":"Full Application","chinDesc":"","attributes":[{}]},
--        {"code":"S","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]},
--        {"code":"Q","engDesc":"	Quick Application","chinDesc":"","attributes":[{}]},
--        {"code":"G","engDesc":"GIO - Guarantee of Insurability Option","chinDesc":"","attributes":[{}]}]',
--        '02-Jan-2023',
--        NULL,
--        'SYSTEM',
--        '02-Jan-2023'
--);

insert into list_of_value values(
    'APP_TYPE',
    CAST(N'[{"code":"","engDesc":"Full Application","chinDesc":"","attributes":[{}]}' AS nvarchar(max))
        +N',{"code":"S","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"G","engDesc":"GIO - Guarantee of Insurability Option","chinDesc":"","attributes":[{}]}'
        -- IFP_25_RL01_NBUW_30056 added start
        +N',{"code":"Q","engDesc":"Quick Application","chinDesc":"","attributes":[{}]}'
        -- IFP_25_RL01_NBUW_30056 added end
        +N']',
        '23-APR-2023',
        NULL,
        'SYSTEM',
        '05-Jan-2025'
);
--IFP_23_RL02_NBUW_6098_3933 end

--240424 IFP_24_RL03_NBUW_20341 modified start

--insert into list_of_value values(
--    'UPSTREAM_APP_TYPE',
--    N'[
--        {"code":"FULL_APP","engDesc":"Full Application","chinDesc":"","attributes":[{}]},
--        {"code":"VHIS_SUQ","engDesc":"Full Application","chinDesc":"","attributes":[{}]},
--        {"code":"GIO","engDesc":"GIO - Guarantee of Insurability Option","chinDesc":"","attributes":[{}]},
--        {"code":"SIO_PA","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]},
--        {"code":"SIO_KH","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]},
--        {"code":"SIO","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]},
--        {"code":"SIO_CPI","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]},
--        {"code":"QUICK_APPLICATION","engDesc":"Quick Application","chinDesc":"","attributes":[{}]}]',
--        '02-Jan-2023',
--        NULL,
--        'SYSTEM',
--        '02-Jan-2023'
--);

insert into list_of_value values(
    'UPSTREAM_APP_TYPE',
    cast(N'[' as nvarchar(max))
        +N'{"code":"FULL_APP","engDesc":"Full Application","chinDesc":"","attributes":[{}]}'
        +N',{"code":"VHIS_SUQ","engDesc":"Full Application","chinDesc":"","attributes":[{}]}'
        +N',{"code":"HNW","engDesc":"Full Application","chinDesc":"","attributes":[{}]}'
        +N',{"code":"GIO","engDesc":"GIO - Guarantee of Insurability Option","chinDesc":"","attributes":[{}]}'
        +N',{"code":"SIO_PA","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"SIO_KH","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"SIO_CPB","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"SIO_GEN","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"SIO","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"SIO_CPI","engDesc":"SIO - Simplified Issue Offer","chinDesc":"","attributes":[{}]}'
        +N',{"code":"QUICK_APPLICATION","engDesc":"Quick Application","chinDesc":"","attributes":[{}]}'
        +N']',
        '21-Jul-2024',
        NULL,
        'SYSTEM',
        '21-Jul-2024'
);

--240424 IFP_24_RL03_NBUW_20341 modified end

commit;
