-------------------------------------------------------------------------------------------
-- Mod. Date : 13 Apr 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL05_JMIFUM_328
-- Mod. Desc : 2022 May Release - AKS Migration Development - hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'MC_FNA_FORM';

insert into SYS_CONFIG values(
    'MC_FNA_FORM',
    N'[{
       		"finProtectAdverObjInd": "4101",
       		"finProtectAdverObjAmt": "4102",
       		"provideIncomeObjInd": "4103",
       		"savingObjInd": "4104",
       		"investObjInd": "4105",
       		"healthCareObjInd": "4106",
       		"healthCareObjAmt": "4107",
       		"othrObjInd": "4108",
       		"othrObjSuppl": "4108",
       		"pureInsuranceProduct": "4201",
       		"cumSavingsProduct": "4202",
       		"cumInvestmentProductByInsurer": "4203",
       		"currentInvestmentObj": "4204",
       		"cumInvestmentProductByPh": "4205",
       		"targetProductOthrObjInd": "4206",
       		"targetProductOthrObjSuppl": "4206",
       		"targetProtectPeriod": "4301",
       		"loanForIlasProduct": "4400",
       		"avgMthlyIncome": "4401",
       		"avgMthlyIncomeAmt": "4402",
       		"avgMthlyIncomeAmtByRange": "4403",
       		"avgMthlyExpenseAmt": "4404",
       		"premPerDisposableIncomePct": "4405",
       		"sourceOfMthlyIncome": "4406",
       		"liquidAssetType": "4407",
       		"liquidAssetAmt": "4408",
       		"premPerLiquidAssetPct": "4409",
       		"sourceOfLiquidAsset": "4410",
       		"regPayPeriod": "4411",
       		"regPayWageEarnerInd": "4412",
       		"regPayRetirementAge": "4414",
       		"payAfterRetirementAgeInd": "4415",
       		"sourceOfFuturePrem": "4416",
       		"reasonNotDisclose": "4501",
       		"declarationAcceptProtectionPeriod": "4801",
       		"declarationAcceptProtectionLv": "4802",
       		"declarationPurchaseIlas": "4803",
       		"declarationOthrObjSuppl": "4899"
       }]',
    NULL,
    'SYSTEM',
    '03-May-2022');

commit;
