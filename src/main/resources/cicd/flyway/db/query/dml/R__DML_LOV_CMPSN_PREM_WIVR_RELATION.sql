------------------------------------------------------------------------------------------
-- Mod. Date : 3 Mar 2020
-- Mod. By   : <PERSON>era EG
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'CMPSN_PREM_WIVR_RELATION';

insert into list_of_value values('CMPSN_PREM_WIVR_RELATION',N'[{"code":"FPH","engDesc":"Policyowner''s husband","chinDesc":"保單持有人之丈夫","attributes":[{"nbwbCode":"FPH","eposCode":""}]}, {"code":"FPW","engDesc":"Policyowner''s wife","chinDesc":"保單持有人之妻子","attributes":[{"nbwbCode":"FPW","eposCode":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
