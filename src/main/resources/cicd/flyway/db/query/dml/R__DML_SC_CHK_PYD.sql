-------------------------------------------------------------------------------------------
-- Mod. Date : 27 Apr 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL03_NBUW_488
-- Mod. Desc : 2022 May Release - ManuGlobal Saver - Multi Currency Plan (Version 2)
-------------------------------------------------------------------------------------------
-- Mod. Date : 01 Sep 2022
-- Mod. By   : (MITDC CD) Freya Fu
-- Mod. ref  : IFP_22_RL05_NBUW_1935
-- Mod. Desc : 2022 Nov Release - New Par Saving Product Development
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Sep 2022
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_22_RL05_NBUW_3632
-- Mod. Desc : 2022 Nov Release - Add products for “PYD” follows Issue Date
-------------------------------------------------------------------------------------------
-- Mod. Date : 01 Mar 2023
-- Mod. By   : (MITDC CD) Tim Y Cui
-- Mod. ref  : IFP_23_RL02_NBUW_6277
-- Mod. Desc : 2023 Apr Release - Support PP/PS Regular pay - Apr release
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Mar 2023
-- Mod. By   : (MITDC CD) Kelly Xiao
-- Mod. ref  : IFP_23_RL02_NBUW_6280
-- Mod. Desc : 2023 Apr Release - Support New Annuity plan - Apr release
-------------------------------------------------------------------------------------------
-- Mod. Date : 25 Apr 2023
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_23_RL04_NBUW_7176
-- Mod. Desc : 2023 Jul Release - Data Capture & Update - Plan and Coverage (ILAS)
--------------------------------------------------------------------------------------------
 -- Mod. Date : 06 Jun 2023
 -- Mod. By   : (MITDC CD) Bruce Hu
 -- Mod. ref  : IFP_23_RL04_NBUW_4901
 -- Mod. Desc : 2023 Jul Release - Support rest of the Traditional Products in NBUWS
--------------------------------------------------------------------------------------------
-- Mod. Date : 25 Jul 2022
-- Mod. By   : (MITDC CD) Ivan Wang
-- Mod. ref  : IFP_23_RL05_NBUW_7642
-- Mod. Desc : 2023 Nov Release - Data Capture & Update - UL/HNW (Part 2)
-------------------------------------------------------------------------------------------
 -- Mod. Date : 20 Sep 2023
 -- Mod. By   : (MITDC CD) Darnell Wu
 -- Mod. ref  : IFP_23_RL05_NBUW_11285
 -- Mod. Desc : 2023 Nov Release - Support HK VHIS Rider
--------------------------------------------------------------------------------------------
-- Mod. Date : 27 Sep 2023
-- Mod. By   : (MITDC CD) Kelly Xiao
-- Mod. ref  : IFP_23_RL05_NBUW_11325
-- Mod. Desc : Support Macau non-VHIS Supreme Basic plan - Nov 2023 Release
-------------------------------------------------------------------------------------------
-- Mod. Date : 27 Feb 2024
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_24_RL02_NBUW_14110_BE_B
-- Mod. Desc : 2024 Apr Release - 2024 New Product - Genesis (NBUWS)
-------------------------------------------------------------------------------------------
-- Mod. Date : 14 Mar 2024
-- Mod. By   : (MITDC CD) Ives Wang
-- Mod. ref  : IFP_24_RL02_NBUW_14146_I
-- Mod. Desc : 2024 Apr Release - Support WIOP3 in NB2.0
-------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) Bean Wang
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------
--  Mod. Date : 26 Nov 2024
--  Mod. By   : (MITDC CD) Parker Song
--  Mod. ref  : IFP_25_RL01_NBUW_29525
--  Mod. Desc : 2025 Jan Release - 2024 New Product - New Saving (NBUWS)
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'PYD_ADJUST_CHECK';


-- IFP_23_RL04_NBUW_7176 start
--<<IFP_22_RL03_NBUW_488 start
--insert into sys_config values('PYD_ADJUST_CHECK',
--							'[
--							 {"planCode":"PU500","schmCode":"WE012100","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"},
--                             {"planCode":"PU500","schmCode":"WE012101","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"},
--                             {"planCode":"RK500","schmCode":"RC051801","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "2"},
--                             {"planCode":"RK500","schmCode":"RC051802","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "2"},
--							 {"planCode":"PU500","schmCode":"WB012202","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"},
--                             {"planCode":"PU500","schmCode":"WB032202","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"},
--                             {"planCode":"PU500","schmCode":"WB012203","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"},
--							 {"planCode":"PU500","schmCode":"WB032203","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"},
--							 {"planCode":"RS500","schmCode":"RC012301","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*"}
--							 ]','15-May-2022','SYSTEM','23-Apr-2023');
-->> IFP_22_RL03_NBUW_488 end

insert into sys_config values('PYD_ADJUST_CHECK',
							CAST(N'['AS nvarchar(max))
                            -- IFP_23_RL05_NBUW_11285 modified start
                            -- Change Details:
                            -- PydAdjustCheck pydAdjustCheck = SysConfigUtil.getPydAdjustCheck(SysConfigCategoryConst.PYD_ADJUST_CHECK,getBasicCoveragePlan(), this.getPaymentMethod());
                            -- Change details: Set the PLAN priority when changing PYD (the larger the number, the higher the priority), and set which one will be used when updating PYD
                            -- pydAdjustInd":"I" --> Issue Date   "pydAdjustInd":"A"  --> App Sign Date
                            -- "priority": "1": The larger the parameter, the higher the priority.
                            -- Before change: subject to basic plan(eg: basic: PU500/WE012100 rider: HU599  --> PU500/WE012100)
                            -- After  change: If the priority of HU599 is set and the priority is greater than PU500/WE012100 (eg: basic: PU500/WE012100 rider: HU599  --> HU599)
							+N'{"planCode":"PU500","schmCode":"WE012100","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"PU500","schmCode":"WE012101","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"RK500","schmCode":"RC051801","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "2","priority": 1}'
                            +N',{"planCode":"RK500","schmCode":"RC051802","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "2","priority": 1}'
							+N',{"planCode":"PU500","schmCode":"WB012202","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"PU500","schmCode":"WB032202","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"PU500","schmCode":"WB012203","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							+N',{"planCode":"PU500","schmCode":"WB032203","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							-- IFP_24_RL02_NBUW_14110_BE_B start
                            +N',{"planCode":"PU500","schmCode":"WB012400","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "S","priority": 1}'
                            -- IFP_24_RL02_NBUW_14110_BE_B end
							+N',{"planCode":"RS500","schmCode":"RC012301","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							+N',{"planCode":"MS105","schmCode":null,"coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							+N',{"planCode":"MSD01","schmCode":null,"coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							-- 20240712 IFP_24_RL04_NBUW_23637 added start
							+N',{"planCode":"MT110","schmCode":null,"coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"MT115","schmCode":null,"coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"MT120","schmCode":null,"coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							-- 20240712 IFP_24_RL04_NBUW_23637 added end
							--IFP_23_RL04_NBUW_4901 start
                            +N',{"planCode":"PU500","schmCode":"WB012000","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"PU500","schmCode":"WB012101","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            +N',{"planCode":"FF300","schmCode": "*","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							--IFP_23_RL04_NBUW_4901 end
							-- 20230725 --IFP_23_RL05_NBUW_7642 start
							+N',{"planCode":"UN501","schmCode": "*","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							+N',{"planCode":"UJ501","schmCode": "*","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							--IFP_24_RL02_NBUW_14146_I Start
							+N',{"planCode":"UK501","schmCode": "*","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
							--IFP_24_RL02_NBUW_14146_I end
							-- 20230725 --IFP_23_RL05_NBUW_7642 end
							+N',{"planCode":"HU599","schmCode": null,"coverageClass":"*","pydAdjustInd":"I","paymentMethod": "*","priority": 5}'
							-- IFP_23_RL05_NBUW_11285 modified end
                            -- 20230927 --IFP_23_RL05_NBUW_11325 start
                            +N',{"planCode":"HX599","schmCode": null,"coverageClass":"*","pydAdjustInd":"I","paymentMethod": "*","priority": 1}'
                            -- 20230927 --IFP_23_RL05_NBUW_11325 end
							-- IFP_25_RL01_NBUW_29525 added start
                            +N',{"planCode":"PU500","schmCode":"WB012500","coverageClass":null,"pydAdjustInd":"I","paymentMethod": "S","priority": 1}'
                            -- IFP_25_RL01_NBUW_29525 added end
							+N']','23-Jul-2023','SYSTEM','05-Jan-2025');

-- IFP_23_RL04_NBUW_7176 end
commit;
