------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'CURRENCY_EPI';

insert into list_of_value values('CURRENCY_EPI',N'[{"code":"04","engDesc":"HKD","chinDesc":"港元","attributes":[{"nbwbCode":"04","eposCode":"HKD"}]}, {"code":"02","engDesc":"USD","chinDesc":"美元","attributes":[{"nbwbCode":"02","eposCode":"USD"}]}, {"code":"31","engDesc":"MOP","chinDesc":"澳門元","attributes":[{"nbwbCode":"31","eposCode":"MOP"}]}, {"code":"90","engDesc":"CNY","chinDesc":"人民幣","attributes":[{"nbwbCode":"90","eposCode":"CNY"}]}, {"code":"01","engDesc":"CAD","chinDesc":"加元","attributes":[{"nbwbCode":"01","eposCode":"CAD"}]}, {"code":"12","engDesc":"GBP","chinDesc":"英鎊","attributes":[{"nbwbCode":"12","eposCode":"GBP"}]}, {"code":"23","engDesc":"AUD","chinDesc":"澳元","attributes":[{"nbwbCode":"23","eposCode":"AUD"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
