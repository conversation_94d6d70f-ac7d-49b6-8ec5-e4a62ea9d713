-- Mod. Date : 16 May 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_7934
-- Mod. Desc : Data Capture & Update - Fund (for ILAS)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'RISK_LEVEL_CODE';

insert into list_of_value values('RISK_LEVEL_CODE',
        CAST(N'[' AS nvarchar(max))
            +N'{"code":"1","engDesc":"Low","chinDesc":"","attributes":[{}]}'
            +N',{"code":"2","engDesc":"Low to Medium","chinDesc":"","attributes":[{}]}'
            +N',{"code":"3","engDesc":"Medium","chinDesc":"","attributes":[{}]}'
            +N',{"code":"4","engDesc":"Medium to High","chinDesc":"","attributes":[{}]}'
            +N',{"code":"5","engDesc":"High","chinDesc":"","attributes":[{}]}'
        +N']',
		'23-Jul-2023',
		NULL,
		'SYSTEM',
		'23-Jul-2023');
commit;
