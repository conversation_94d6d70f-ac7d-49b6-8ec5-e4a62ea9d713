------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 06 Jul 2022
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_22_RL04_NBUW_1590
-- Mod. Desc : "Save" Behavior in NBUWS
-------------------------------------------------------------------------------------------
-- Mod. Date : 26 Jan 2023
-- Mod. By   : (MITDC CD) Bruce Hu
-- Mod. ref  : IFP_23_RL02_NBUW_2357
-- Mod. Desc : Data Capture - ManuCard
-------------------------------------------------------------------------------------------
-- Mod. Date : 19 Sep 2024
-- Mod. By   : (MITDC CD) Haly Huang
-- Mod. ref  : IFP_24_RL05_NBUW_27098
-- Mod. Desc : Remove Manucard from payment method
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'PAYMENT_METHOD';

--insert into list_of_value values(
--    'PAYMENT_METHOD',
--    N'[
--        {"code":"1","engDesc":"Direct Billing","chinDesc":"郵寄帳單","attributes":[{"nbwbCode":"1","eposCode":"1"}]}
--       ,{"code":"4","engDesc":"Autopay","chinDesc":"自動轉帳","attributes":[{"nbwbCode":"4","eposCode":"4"}]}
--       ,{"code":"2","engDesc":"PDF - Premium Dump-In Facility","chinDesc":"","attributes":[{"nbwbCode":"2","eposCode":"2"}]}
--       ,{"code":"S","engDesc":"Single Premium","chinDesc":"","attributes":[{"nbwbCode":"S","eposCode":"S"}]}
--    ]',
--    '08-NOV-2020',
--    NULL,
--    'SYSTEM',
--    '07-Jul-2022'
--);

--insert into list_of_value values(
--    'PAYMENT_METHOD',
--    CAST(N'[{"code":"1","engDesc":"Direct Billing","chinDesc":"郵寄帳單","attributes":[{"nbwbCode":"1","eposCode":"1"}]}' AS nvarchar(max))
--       +N',{"code":"4","engDesc":"Autopay","chinDesc":"自動轉帳","attributes":[{"nbwbCode":"4","eposCode":"4"}]}'
--       +N',{"code":"2","engDesc":"PDF - Premium Dump-In Facility","chinDesc":"","attributes":[{"nbwbCode":"2","eposCode":"2"}]}'
--       +N',{"code":"S","engDesc":"Single Premium","chinDesc":"","attributes":[{"nbwbCode":"S","eposCode":"S"}]}'
--       +N',{"code":"9","engDesc":"ManuCard","chinDesc":"","attributes":[{"nbwbCode":"9","eposCode":"9"}]}'
--    +N']',
--  '23-APR-2023',
--  NULL,
--  'SYSTEM',
--  '23-APR-2023'
--);

insert into list_of_value values(
    'PAYMENT_METHOD',
    CAST(N'[{"code":"1","engDesc":"Direct Billing","chinDesc":"郵寄帳單","attributes":[{"nbwbCode":"1","eposCode":"1","isExpired":"N"}]}' AS nvarchar(max))
       +N',{"code":"4","engDesc":"Autopay","chinDesc":"自動轉帳","attributes":[{"nbwbCode":"4","eposCode":"4","isExpired":"N"}]}'
       +N',{"code":"2","engDesc":"PDF - Premium Dump-In Facility","chinDesc":"","attributes":[{"nbwbCode":"2","eposCode":"2","isExpired":"N"}]}'
       +N',{"code":"S","engDesc":"Single Premium","chinDesc":"","attributes":[{"nbwbCode":"S","eposCode":"S","isExpired":"N"}]}'
       +N',{"code":"9","engDesc":"ManuCard","chinDesc":"","attributes":[{"nbwbCode":"9","eposCode":"9","isExpired":"Y"}]}'
     +N']',
    '23-APR-2023',
    NULL,
    'SYSTEM',
    '23-APR-2023'
);

commit;
