-------------------------------------------------------------------------------------------
-- Mod. Date : 13 Apr 2022
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_22_RL05_JMIFUM_328
-- Mod. Desc : 2022 May Release - AKS Migration Development - hk-ifp-nb-core-service
-------------------------------------------------------------------------------------------
-- Mod. Date : 06 Jun 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL04_NBUW_668
-- Mod. Desc : 2022 July Release - IA Premium Financing
-------------------------------------------------------------------------------------------
-- Mod. Date : 05 Aug 2022
-- Mod. By   : (MITDC CD) Everrett Xiang
-- Mod. ref  : IFP_22_RL04_NBUW_1540
-- Mod. Desc : 2022 Nov Release - IA Premium Financing
-------------------------------------------------------------------------------------------
 -- Mod. Date : 12 Jul 2024
 -- Mod. By   : (MITDC CD) Bean Wang
 -- Mod. ref  : IFP_24_RL04_NBUW_23637
 -- Mod. Desc : Support RP ILAS in NBUWS - Sep 2024 Release
--------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'FNA_FORM';

    insert into SYS_CONFIG values(
        'FNA_FORM',
        CAST(N'[{' AS nvarchar(max))
           		+N'"finProtectAdverObjInd": "3101",'
           		+N'"finProtectAdverObjAmt": "3102",'
           		+N'"provideIncomeObjInd": "3103",'
           		+N'"savingObjInd": "3104",'
           		+N'"savingObjAmt": "3105",'
           		+N'"propTargetSavingAmtULPlan": "3113",'
           		+N'"savingObjPeriod": "3107",'
           		+N'"investObjInd": "3108",'
           		+N'"investChoice": "3109",'
           		+N'"healthCareObjInd": "3110",'
           		+N'"healthCareObjAmt": "3111",'
           		+N'"othrObjInd": "3112",'
           		+N'"targetProtectPeriod": "3201",'
           		+N'"avgMthlyIncomeAmt": "3301",'
           		+N'"avgMthlyExpenseAmt": "3302",'
           		+N'"premPerDisposableIncomePct": "3303",'
           		+N'"sourceOfMthlyIncome": "3306",'
           		+N'"liquidAssetAmt": "3304",'
           		+N'"premPerLiquidAssetPct": "3305",'
           		+N'"sourceOfLiquidAsset": "3307",'
           		+N'"useOfPremFin": "3308",'
                +N'"existPremFin": "3309",'
                +N'"existLoanOfPFNotMoreThan": "3310",'
                +N'"existLoanByCreditInd": "3311",'
                +N'"existLoanByOthersInd": "3312",'
                +N'"existLiabilitiesNotMoreThan": "3313",'
                +N'"existMortgageLoanNotMoreThan": "3314",'
           		+N'"regPayInd": "3401",'
           		+N'"regPayPeriod": "3402",'
           		+N'"regPayWageEarnerInd": "3403",'
           		+N'"regPayRetirementAge": "3405",'
           		+N'"payAfterRetirementAgeInd": "3407",'
           		+N'"sourceOfFuturePrem": "3408",'
           		+N'"singlePayInd": "3406",'
           		+N'"advisorReasonDiffProtectPeriodInd": "3601",'
           		+N'"advisorReasonLowProtectLevelInd": "3602",'
           		+N'"advisorReasonLowSavingAmtInd": "3603",'
           		+N'"advisorReasonPreferMismatchInvestInd": "3607",'
           		-- IFP_24_RL04_NBUW_23637 added start
           		+N'"advisorReasonRPILASPreferMismatchInvestInd": "3611",'
           		-- IFP_24_RL04_NBUW_23637 added end
           		+N'"advisorReasonPremFinInd": "3608",'
           		+N'"advisorReasonLongerPPPInd": "3609",'
           		+N'"advisorReasonOtherInd": "3604",'
           		+N'"ifsPFSubmittedInd": "501",'
           		+N'"companyHasRecommendedPF" : "502",'
           		+N'"providePFInfoInd": "503",'
           		+N'"lenderName": "504",'
           		+N'"lenderNameOfOther": "505",'
           		+N'"loanAmount": "506",'
           		+N'"loanAnnualizedInterestRate": "507",'
           		+N'"loanTenor": "508",'
           		+N'"repaymentAmountForEachInstalment": "511",'
           		+N'"notProvidePFInfoInd": "510",'
                +N'"internalUseTotalRepaymentAmount": "512",'
                +N'"newPfAdvisorReason": "3610"'

           +N'}]',
        '06-Jun-2022',
        'SYSTEM',
        '08-Sep-2024');
--IFP_22_RL04_NBUW_668 end
commit;
