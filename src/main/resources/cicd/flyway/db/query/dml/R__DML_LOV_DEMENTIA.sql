-------------------------------------------------------------------------------------------
-- Mod. Date : 22 Mar 2023
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_6936
-- Mod. Desc : Data Update - Plan Info - DementiaDeclaredInd
-------------------------------------------------------------------------------------------

delete from list_of_value where LIST_CODE = 'DEMENTIA_IN_SCOPE_PRODUCT';

insert into list_of_value values(
    'DEMENTIA_IN_SCOPE_PRODUCT',
    CAST(N'[' AS nvarchar(max))
        +N'{"code":"RS500|RC012301","engDesc":"FlexiFortune Annuity Plan, Single Premium","chinDesc":"","attributes":[{}]}'
        +N',{"code":"RS500|RC032301","engDesc":"FlexiFortune Annuity Plan, Paid up in 3 years","chinDesc":"","attributes":[{}]}'
        +N',{"code":"RS500|RC052301","engDesc":"FlexiFortune Annuity Plan, Paid up in 5 years","chinDesc":"","attributes":[{}]}'
        +N',{"code":"RS500|RC102301","engDesc":"FlexiFortune Annuity Plan, Paid up in 10 years","chinDesc":"","attributes":[{}]}'

     +N']',
     '23-Apr-2023',
     NULL,
     'SYSTEM',
     '23-Apr-2023'
);

commit;
