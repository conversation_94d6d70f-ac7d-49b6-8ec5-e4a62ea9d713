------------------------------------------------------------------------------------------
-- Mod. Date : 01 Jul 2022
-- Mod. By   : (MITDC CD) Yale Liu
-- Mod. ref  : IFP_22_RL04_NBUW_1634
-- Mod. Desc : ID Holder Control for Medical & Accident Plan/Rider
-------------------------------------------------------------------------------------------
-- Mod. Date : 22 Mar 2023
-- Mod. By   : (MITDC CD) Dar<PERSON> Wu
-- Mod. ref  : IFP_23_RL02_NBUW_6279
-- Mod. Desc : 2023 Apr Release - Support <PERSON>ushine for Macau and MCV (ePOS & NBUWS) - Apr release(ADD HO599)
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'PLAN_RES_LEGALID_CHK';

--<< 230322 IFP_23_RL02_NBUW_6279 modified start
/*
insert into sys_config values(
    'PLAN_RES_LEGALID_CHK',
    N'[
          {
            "planCode": "CF599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4",
            "country": "HK,MO"
          },
          {
            "planCode": "HR599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4",
            "country": "HK,MO"
          },
          {
            "planCode": "HN599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4",
            "country": "HK,MO"
          }
        ]',
    '24-Jul-2022',
    'SYSTEM',
    '24-Jul-2022'
);
*/


insert into sys_config values(
    'PLAN_RES_LEGALID_CHK',
    N'[
          {
            "planCode": "CF599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4",
            "country": "HK,MO"
          },
          {
            "planCode": "HR599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3",
            "country": "*"
          },
          {
            "planCode": "HN599",
            "schmCode": "*",
            "coverageClass": "*",
            "legalIdType": "2,3,4",
            "country": "HK,MO"
          },
          {
             "planCode": "HO599",
             "schmCode": "*",
             "coverageClass": "*",
             "legalIdType": "2,3",
             "country": "*"
          }
        ]',
    '24-Jul-2022',
    'SYSTEM',
    '23-Apr-2023'
);
-->> 230322 IFP_23_RL02_NBUW_6279 modified end

commit;
