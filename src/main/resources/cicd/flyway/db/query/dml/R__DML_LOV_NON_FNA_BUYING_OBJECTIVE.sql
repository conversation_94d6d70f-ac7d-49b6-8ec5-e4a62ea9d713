------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Jan 2021
-- Mod. By   : Meera EG
-- Mod. ref  : IFP_21_RL02_SB_NB_18742
-- Mod. Desc : [GL31] Data Modal Support in March release (NB2.0 development / IGG-1656) (https://jira.ap.manulife.com/browse/SB-18742)
-------------------------------------------------------------------------------------------
-- Mod. Date : 08 Dec 2021
-- Mod. By   : Cherry Wong
-- Mod. ref  : IFP_22_RL01_SB_NB_23583
-- Mod. Desc : Item 18 - update "Others" option description in LOV for SOW / SOF / Buying Objective
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'NON_FNA_BUYING_OBJECTIVE';

--IFP_21_RL02_SB_NB_18742 start
--insert into list_of_value values('NON_FNA_BUYING_OBJECTIVE',N'[{"code":"A","engDesc":"Financial protection against adversities - Life (Death)","chinDesc":"為應付不時之需的財務保障—人壽（身故）","attributes":[{"nbwbCode":"A","eposCode":"A"}]}, {"code":"B","engDesc":"Financial protection against adversities - Disability, Accident","chinDesc":"為應付不時之需的財務保障—意外、殘疾","attributes":[{"nbwbCode":"B","eposCode":"B"}]}, {"code":"C","engDesc":"Provide regular income in the future","chinDesc":"為未來提供定期的收入","attributes":[{"nbwbCode":"C","eposCode":"C"}]}, {"code":"D","engDesc":"Saving up for the future","chinDesc":"為未來需要儲蓄","attributes":[{"nbwbCode":"D","eposCode":"D"}]}, {"code":"E","engDesc":"Investment","chinDesc":"投資","attributes":[{"nbwbCode":"E","eposCode":"E"}]}, {"code":"F","engDesc":"Preparation for health care needs - Critical Illness","chinDesc":"為醫療需要作準備—危疾","attributes":[{"nbwbCode":"F","eposCode":"F"}]}, {"code":"G","engDesc":"Preparation for health care needs - Hospitalization","chinDesc":"為醫療需要作準備—住院","attributes":[{"nbwbCode":"G","eposCode":"G"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

--insert into list_of_value values('NON_FNA_BUYING_OBJECTIVE',N'[{"code":"A","engDesc":"Financial protection against adversities","chinDesc":"為應付不時之需的財務保障","attributes":[{"nbwbCode":"A","eposCode":"A"}]}, {"code":"B","engDesc":"Providing regular income in the future","chinDesc":"為未來提供定期的收入","attributes":[{"nbwbCode":"B","eposCode":"B"}]}, {"code":"C","engDesc":"Saving up for the future","chinDesc":"為未來需要儲蓄","attributes":[{"nbwbCode":"C","eposCode":"C"}]}, {"code":"D","engDesc":"Investment","chinDesc":"投資","attributes":[{"nbwbCode":"D","eposCode":"D"}]}, {"code":"E","engDesc":"Preparation for health care needs","chinDesc":"為醫療需要作準備","attributes":[{"nbwbCode":"E","eposCode":"E"}]}, {"code":"O","engDesc":"Others(Please specify)","chinDesc":"其他（請註明）","attributes":[{"nbwbCode":"O","eposCode":"O"}]}]','08-NOV-2020',NULL,'SYSTEM','07-MAR-2021');

--IFP_21_RL02_SB_NB_18742 end

--IFP_22_RL01_SB_NB_23583 start

insert into list_of_value values('NON_FNA_BUYING_OBJECTIVE',N'[{"code":"A","engDesc":"Financial protection against adversities","chinDesc":"為應付不時之需的財務保障","attributes":[{"nbwbCode":"A","eposCode":"A"}]}, {"code":"B","engDesc":"Providing regular income in the future","chinDesc":"為未來提供定期的收入","attributes":[{"nbwbCode":"B","eposCode":"B"}]}, {"code":"C","engDesc":"Saving up for the future","chinDesc":"為未來需要儲蓄","attributes":[{"nbwbCode":"C","eposCode":"C"}]}, {"code":"D","engDesc":"Investment","chinDesc":"投資","attributes":[{"nbwbCode":"D","eposCode":"D"}]}, {"code":"E","engDesc":"Preparation for health care needs","chinDesc":"為醫療需要作準備","attributes":[{"nbwbCode":"E","eposCode":"E"}]}, {"code":"O","engDesc":"Others","chinDesc":"其他","attributes":[{"nbwbCode":"O","eposCode":"O"}]}]','08-NOV-2020',NULL,'SYSTEM','02-Jan-2022');

--IFP_22_RL01_SB_NB_23583 end
commit;
