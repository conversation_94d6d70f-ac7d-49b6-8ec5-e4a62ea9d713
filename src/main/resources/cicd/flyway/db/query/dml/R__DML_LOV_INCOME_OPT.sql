------------------------------------------------------------------------------------------
-- Mod. Date : 15 Mar 2021
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_21_RL03_SB_NB_19093 
-- Mod. Desc : Support data fields for MVP4 products from ePOS integration (Annuity Option/ Annuity Income Payment Option)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'INCOME_OPT';

insert into list_of_value values('INCOME_OPT',N'[{"code":"1","engDesc":"Leave on Deposit with Interest","chinDesc":"積存生息","attributes":[{"nbwbCode":"1","eposValue":""}]},{"code":"2","engDesc":"Payout Monthly","chinDesc":"每月支付","attributes":[{"nbwbCode":"2","eposValue":""}]}]','16-MAY-2021',NULL,'SYSTEM','16-MAY-2021');

commit;

