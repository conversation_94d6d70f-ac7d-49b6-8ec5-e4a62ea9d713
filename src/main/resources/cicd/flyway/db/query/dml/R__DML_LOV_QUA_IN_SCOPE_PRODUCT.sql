-------------------------------------------------------------------------------------------
 -- Mod. Date : 24 Oct 2024
 -- Mod. By   : (MITDC CD) <PERSON>
 -- Mod. ref  : IFP_25_RL01_NBUW_30056
 -- Mod. Desc : Support Quick App in NBUWS
--------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'QUA_IN_SCOPE_PRODUCT';

insert into LIST_OF_VALUE
values(
        'QUA_IN_SCOPE_PRODUCT',
        CAST(N'[' AS nvarchar(max))
		    +N'{"code":"MV510","engDesc":"Alpha Regular Investor, Premium Payment Period 10 Years","chinDesc":"","attributes":[{}]}'
		    +N',{"code":"MV520","engDesc":"Alpha Regular Investor, Premium Payment Period 20 Years","chinDesc":"","attributes":[{}]}'
		    +N',{"code":"MV525","engDesc":"Alpha Regular Investor, Premium Payment Period 25 Years","chinDesc":"","attributes":[{}]}'
		    +N',{"code":"MV530","engDesc":"Alpha Regular Investor, Premium Payment Period 30 Years","chinDesc":"","attributes":[{}]}'
		    +N',{"code":"MT110","engDesc":"Apollo Protection Linked Plan, Premium Payment Period 10 Years","chinDesc":"","attributes":[{}]}'
		    +N',{"code":"MT115","engDesc":"Apollo Protection Linked Plan, Premium Payment Period 15 Years","chinDesc":"","attributes":[{}]}'
		    +N',{"code":"MT120","engDesc":"Apollo Protection Linked Plan, Premium Payment Period 20 Years","chinDesc":"","attributes":[{}]}'
        +N']',
		'23-Apr-2023',
		NULL,
		'SYSTEM',
		'05-Jan-2025');

commit;