------------------------------------------------------------------------------------------
-- Mod. Date : 21 Apr 2023
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_23_RL04_NBUW_2499
-- Mod. Desc : Data Update - Regular Payout for Saving Products
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'WITHDRAWAL_CURRENCY';

insert into list_of_value values(
    'WITHDRAWAL_CURRENCY',
    N'[
        {"code":"04","engDesc":"HKD","chinDesc":"","attributes":[{"nbwbCode": "04","eposCode": "04"}]},
        {"code":"02","engDesc":"USD","chinDesc":"","attributes":[{"nbwbCode": "02","eposCode": "02"}]},
        {"code":"31","engDesc":"MOP","chinDesc":"","attributes":[{"nbwbCode": "31","eposCode": "31"}]}
    ]',
    '23-JUL-2023',
    NULL,
    'SYSTEM',
    '23-JUL-2023'
);
commit;
