------------------------------------------------------------------------------------------
-- Mod. Date : 3 Sep 2021
-- Mod. By   : (MITDC CD) <PERSON><PERSON>
-- Mod. ref  : IFP_21_RL05_SB_NB_20579
-- Mod. Desc : Retrieve Payment Information in NBUWS (BE)
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'CASHIER_PAYMENT_TYPE';

insert into list_of_value values(
    'CASHIER_PAYMENT_TYPE',
    N'[
        {"code":"01","engDesc":"Cheque - CTB","chinDesc":"","attributes":[{"nbwbCode":"01","eposCode":"01"}]},
        {"code":"02","engDesc":"Cash","chinDesc":"","attributes":[{"nbwbCode":"02","eposCode":"02"}]},
        {"code":"03","engDesc":"CCAA (AE)","chinDesc":"","attributes":[{"nbwbCode":"03","eposCode":"03"}]},
        {"code":"04","engDesc":"CCAA (Manucard)","chinDesc":"","attributes":[{"nbwbCode":"04","eposCode":"04"}]},
        {"code":"05","engDesc":"CCAA (VISA/Mastercard)","chinDesc":"","attributes":[{"nbwbCode":"05","eposCode":"05"}]},
        {"code":"06","engDesc":"Cancelled CQ","chinDesc":"","attributes":[{"nbwbCode":"06","eposCode":"06"}]},
        {"code":"07","engDesc":"Non-refundable coupon","chinDesc":"","attributes":[{"nbwbCode":"07","eposCode":"07"}]},
        {"code":"08","engDesc":"Stale dated CQ","chinDesc":"","attributes":[{"nbwbCode":"08","eposCode":"08"}]},
        {"code":"09","engDesc":"PPS","chinDesc":"","attributes":[{"nbwbCode":"09","eposCode":"09"}]},
        {"code":"10","engDesc":"SI","chinDesc":"","attributes":[{"nbwbCode":"10","eposCode":"10"}]},
        {"code":"11","engDesc":"Special Handling - Cash Related","chinDesc":"","attributes":[{"nbwbCode":"11","eposCode":"11"}]},
        {"code":"12","engDesc":"Internal transfer (In)","chinDesc":"","attributes":[{"nbwbCode":"12","eposCode":"12"}]},
        {"code":"13","engDesc":"NB initial payment","chinDesc":"","attributes":[{"nbwbCode":"13","eposCode":"13"}]},
        {"code":"14","engDesc":"Cheque / Draft","chinDesc":"","attributes":[{"nbwbCode":"14","eposCode":"14"}]},
        {"code":"15","engDesc":"Bounced Cheque","chinDesc":"","attributes":[{"nbwbCode":"15","eposCode":"15"}]},
        {"code":"16","engDesc":"Spec Handling - Cash Related","chinDesc":"","attributes":[{"nbwbCode":"16","eposCode":"16"}]},
        {"code":"17","engDesc":"Internal Transfer (Out)","chinDesc":"","attributes":[{"nbwbCode":"17","eposCode":"17"}]},
        {"code":"18","engDesc":"Automated Cheque","chinDesc":"","attributes":[{"nbwbCode":"18","eposCode":"18"}]},
        {"code":"19","engDesc":"Bounced pymt (AE)","chinDesc":"","attributes":[{"nbwbCode":"19","eposCode":"19"}]},
        {"code":"20","engDesc":"Bounced pymt (Manucard)","chinDesc":"","attributes":[{"nbwbCode":"20","eposCode":"20"}]},
        {"code":"21","engDesc":"Bounced pymt (VISA/Mastercard)","chinDesc":"","attributes":[{"nbwbCode":"21","eposCode":"21"}]},
        {"code":"22","engDesc":"NB disbursement","chinDesc":"","attributes":[{"nbwbCode":"22","eposCode":"22"}]},
        {"code":"23","engDesc":"Refundable coupon","chinDesc":"","attributes":[{"nbwbCode":"23","eposCode":"23"}]},
        {"code":"24","engDesc":"ATM","chinDesc":"","attributes":[{"nbwbCode":"24","eposCode":"24"}]},
        {"code":"25","engDesc":"Cheque / Draft (US in HK)","chinDesc":"","attributes":[{"nbwbCode":"25","eposCode":"25"}]},
        {"code":"27","engDesc":"Draft (HK)","chinDesc":"","attributes":[{"nbwbCode":"27","eposCode":"27"}]},
        {"code":"32","engDesc":"Cancelled Autopay Disbursement - With Bank Report","chinDesc":"","attributes":[{"nbwbCode":"32","eposCode":"32"}]},
        {"code":"37","engDesc":"China UnionPay","chinDesc":"","attributes":[{"nbwbCode":"37","eposCode":"37"}]},
        {"code":"38","engDesc":"Bank Transfer - DBS","chinDesc":"","attributes":[{"nbwbCode":"38","eposCode":"38"}]},
        {"code":"39","engDesc":"EPS Payment","chinDesc":"","attributes":[{"nbwbCode":"39","eposCode":"39"}]},
        {"code":"41","engDesc":"Telegraphic Transfer (HSBC","chinDesc":"","attributes":[{"nbwbCode":"41","eposCode":"41"}]},
        {"code":"42","engDesc":"Bank Transfer - CBI","chinDesc":"","attributes":[{"nbwbCode":"42","eposCode":"42"}]},
        {"code":"43","engDesc":"Bank Transfer - BTMU","chinDesc":"","attributes":[{"nbwbCode":"43","eposCode":"43"}]},
        {"code":"44","engDesc":"SAM (VISA/Mastercard)","chinDesc":"","attributes":[{"nbwbCode":"44","eposCode":"44"}]},
        {"code":"45","engDesc":"Cheque - HSBC","chinDesc":"","attributes":[{"nbwbCode":"45","eposCode":"45"}]},
        {"code":"46","engDesc":"Cheque - BOC","chinDesc":"","attributes":[{"nbwbCode":"46","eposCode":"46"}]},
        {"code":"50","engDesc":"Cheque - CTB (MACAU)","chinDesc":"","attributes":[{"nbwbCode":"50","eposCode":"50"}]},
        {"code":"51","engDesc":"eBanking","chinDesc":"","attributes":[{"nbwbCode":"51","eposCode":"51"}]},
        {"code":"52","engDesc":"DRAFT (W/O RECEIPT) - CTB","chinDesc":"","attributes":[{"nbwbCode":"52","eposCode":"52"}]},
        {"code":"53","engDesc":"DRAFT (W/O RECEIPT) - HSBC","chinDesc":"","attributes":[{"nbwbCode":"53","eposCode":"53"}]},
        {"code":"54","engDesc":"DRAFT (W/O RECEIPT) - BOC","chinDesc":"","attributes":[{"nbwbCode":"54","eposCode":"54"}]},
        {"code":"55","engDesc":"DRAFT (W/O RECEIPT) - CTB (Macau)","chinDesc":"","attributes":[{"nbwbCode":"55","eposCode":"55"}]},
        {"code":"56","engDesc":"DRAFT (With RECEIPT) - CTB","chinDesc":"","attributes":[{"nbwbCode":"56","eposCode":"56"}]},
        {"code":"57","engDesc":"DRAFT (With RECEIPT) - HSBC","chinDesc":"","attributes":[{"nbwbCode":"57","eposCode":"57"}]},
        {"code":"58","engDesc":"DRAFT (With RECEIPT) - BOC","chinDesc":"","attributes":[{"nbwbCode":"58","eposCode":"58"}]},
        {"code":"59","engDesc":"DRAFT (With RECEIPT) - CTB (Macau)","chinDesc":"","attributes":[{"nbwbCode":"59","eposCode":"59"}]},
        {"code":"60","engDesc":"All In Pay - (Charge paid by Company)","chinDesc":"","attributes":[{"nbwbCode":"60","eposCode":"60"}]},
        {"code":"61","engDesc":"All In Pay - (Charge paid by Customer)","chinDesc":"","attributes":[{"nbwbCode":"61","eposCode":"61"}]},
        {"code":"65","engDesc":"Cash(CircleK/VanGO/CRVanguard)","chinDesc":"","attributes":[{"nbwbCode":"65","eposCode":"65"}]},
        {"code":"66","engDesc":"Cash (7-Eleven)","chinDesc":"","attributes":[{"nbwbCode":"66","eposCode":"66"}]},
        {"code":"67","engDesc":"UnionPay Online","chinDesc":"","attributes":[{"nbwbCode":"67","eposCode":"67"}]},
        {"code":"68","engDesc":"Special Handling - Non Cash Related","chinDesc":"","attributes":[{"nbwbCode":"68","eposCode":"68"}]},
        {"code":"69","engDesc":"Spec Handling - Non Cash Related","chinDesc":"","attributes":[{"nbwbCode":"69","eposCode":"69"}]},
        {"code":"70","engDesc":"Cancelled Autopay Disbursement - Without Bank Report","chinDesc":"","attributes":[{"nbwbCode":"70","eposCode":"70"}]},
        {"code":"71","engDesc":"Telegraphic Transfer (DBS)","chinDesc":"","attributes":[{"nbwbCode":"71","eposCode":"71"}]},
        {"code":"72","engDesc":"BOC Credit Card - Local","chinDesc":"","attributes":[{"nbwbCode":"72","eposCode":"72"}]},
        {"code":"73","engDesc":"BOC Credit Card - Overseas","chinDesc":"","attributes":[{"nbwbCode":"73","eposCode":"73"}]},
        {"code":"74","engDesc":"Faster Payment System (FPS)","chinDesc":"","attributes":[{"nbwbCode":"74","eposCode":"74"}]},
        {"code":"75","engDesc":"Cash Deposit - Citibank","chinDesc":"","attributes":[{"nbwbCode":"75","eposCode":"75"}]},
        {"code":"77","engDesc":"HSBC Bill Payment","chinDesc":"","attributes":[{"nbwbCode":"77","eposCode":"77"}]},
        {"code":"78","engDesc":"Cancelled All in Pay Disbursement - AIP","chinDesc":"","attributes":[{"nbwbCode":"78","eposCode":"78"}]},
        {"code":"79","engDesc":"Cancelled All in Pay Disbursement - TLT","chinDesc":"","attributes":[{"nbwbCode":"79","eposCode":"79"}]}
    ]',
    '07-NOV-2021',
    NULL,
    'SYSTEM',
    '07-NOV-2021'
);

commit;