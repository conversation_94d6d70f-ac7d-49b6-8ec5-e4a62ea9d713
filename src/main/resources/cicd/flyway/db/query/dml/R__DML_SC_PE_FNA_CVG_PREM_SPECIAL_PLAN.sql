-------------------------------------------------------------------------------------------
--  Mod. Date : 02 Jul 2024
--  Mod. By   : (MITDC CD) <PERSON>
--  Mod. ref  : IFP_24_RL03_NBUW_21047_UAT_BUG_24889
--  Mod. Desc : 2024 Jul Release - Fix BUG for FNA validation is not mismatch between STP&NBUWS
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'PE_FNA_CVG_PREM_SPECIAL_PLAN';

insert into SYS_CONFIG
values
	 ('PE_FNA_CVG_PREM_SPECIAL_PLAN',
	 CAST(N'[' AS nvarchar(max))
		 +N'{"planCode":"HT599","schmCode":"*"}'
        +N']',
		'21-Jul-2024',
		'SYSTEM',
		'21-Jul-2024');
commit;