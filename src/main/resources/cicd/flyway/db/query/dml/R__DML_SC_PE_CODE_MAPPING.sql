------------------------------------------------------------------------------------------
-- Mod. Date : 12 Jan 2023
-- Mod. By   : (MITDC CD) Auter <PERSON>
-- Mod. ref  : IFP_23_RL02_NBUW_6947
-- Mod. Desc : PE validation for NBUWS (Part 1 of 2)
-------------------------------------------------------------------------------------------
-- Mod. Date : 20 Apr 2023
-- Mod. By   : (MITDC CD) Auter Zhang
-- Mod. ref  : IFP_23_RL04_NBUW_6916
-- Mod. Desc : PE validation for NBUWS (Part 2 of 2)
-------------------------------------------------------------------------------------------
-- Mod. Date : 12 Sep 2022
-- Mod. By   : (MITDC CD) Danny Pan
-- Mod. ref  : IFP_23_RL05_NBUW_11436
-- Mod. Desc : Build Residency Mapping & Validation Setup in Customer Tab
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'PE_CODE_MAPPING';

insert into sys_config values(
    'PE_CODE_MAPPING',
    N'[
        {
            "category": "SMOKER_STATUS",
            "values": [
                {
                    "peCode": "NS",
                    "code": "N"
                },
                {
                    "peCode": "NS",
                    "code": "R"
                },
                {
                    "peCode":"ST",
                    "code": "S"
                },
                {
                    "peCode": "ST",
                    "code": "U"
                },
                {
                    "peCode":"XN",
                    "code": "P"
                },
                {
                    "peCode": "ZN",
                    "code": "Q"
                },
                {
                    "peCode":"PN",
                    "code": "O"
                },
                {
                    "peCode":"PS",
                    "code": "T"
                }
            ]
        },
        {
            "category": "PAYMENT_MODE",
            "values": [
                {
                    "peCode": "M",
                    "code": "1"
                },
                {
                    "peCode":"Q",
                    "code": "3"
                },
                {
                    "peCode":"S",
                    "code": "6"
                },
                {
                    "peCode":"A",
                    "code": "12"
                }
            ]
        },
        {
            "category": "DIVIDEND_OPTION",
            "values": [
                {
                    "peCode":"leaveondeposit",
                    "code": "2"
                }
            ]
        },
        {
            "category": "PDF_OPTION",
            "values": [
                {
                    "peCode": "prepayment",
                    "code": "P"
                }
            ]
        },
        {
            "category": "INCOME_OPTION",
            "values": [
                {
                    "peCode": "leaveondeposit",
                    "code": "1"
                },
                {
                    "peCode": "payoutmonthly",
                    "code": "2"
                }
            ]
        },
        {
            "category": "INCOME_ANNUITY_OPTION",
            "values": [
                {
                    "peCode": "livingage85",
                    "code": "RK500|RC051801|1"
                },
                {
                    "peCode": "livingage85",
                    "code": "RK500|RC101801|1"
                },
                {
                    "peCode": "livingage85",
                    "code": "RK500|RC151801|1"
                },
                {
                    "peCode": "livingage85",
                    "code": "RK500|RC201801|1"
                },
                {
                    "peCode": "livingage85",
                    "code": "RK500|RC651801|1"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC051801|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC101801|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC151801|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC201801|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC651801|2"
                },
                {
                    "peCode": "livingage100",
                    "code": "RK500|RC051802|1"
                },
                {
                    "peCode": "livingage100",
                    "code": "RK500|RC101802|1"
                },
                {
                    "peCode": "livingage100",
                    "code": "RK500|RC151802|1"
                },
                {
                    "peCode": "livingage100",
                    "code": "RK500|RC201802|1"
                },
                {
                    "peCode": "livingage100",
                    "code": "RK500|RC651802|1"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC051802|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC101802|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC151802|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC201802|2"
                },
                {
                    "peCode": "certainage100",
                    "code": "RK500|RC651802|2"
                }
            ]
        }
     ]',
    '06-Apr-2023',
    'SYSTEM',
    '06-Apr-2023'
);

commit;
