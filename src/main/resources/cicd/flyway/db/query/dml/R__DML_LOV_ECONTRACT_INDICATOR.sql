------------------------------------------------------------------------------------------
-- Mod. Date : 27 Apr 2022
-- Mod. By   : <PERSON><PERSON> Li
-- Mod. ref  : IFP_22_RL03_NBUW_595
-- Mod. Desc :  eContract indicator support (MVP1) - display on NBUWS
-------------------------------------------------------------------------------------------
-- Mod. Date : 16 Nov 2022
-- Mod. By   : (MITDC CD) Joe Wang
-- Mod. ref  : IFP_23_RL01_NBUW_2450
-- Mod. Desc :  Data Update - Coverage (non-ILAS) (Plan Options)
-------------------------------------------------------------------------------------------
-- Mod. Date : 29 Mar 2023
-- Mod. By   : (MITDC CD) Ivan Wang
-- Mod. ref  : IFP_23_RL02_NBUW_7954
-- Mod. Desc : [FIX] eContract LOV
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'ECONTRACT_INDICATOR';

--<< 221116 IFP_23_RL01_NBUW_2450 modified start
/*
insert into list_of_value values(
    'ECONTRACT_INDICATOR',
    N'[
        {"code":null,"engDesc":"Not applicable","chinDesc":"","attributes":[{"nbwbCode":null,"eposCode":null}]},
        {"code":"Y","engDesc":"Y (Econtract only)","chinDesc":"","attributes":[{"nbwbCode":"Y","eposCode":"Y"}]},
        {"code":"N","engDesc":"N (Paper contract)","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":"N"}]}
    ]',
    '15-May-2022',
    NULL,
    'SYSTEM',
    '15-May-2022'
);
*/

insert into list_of_value values(
    'ECONTRACT_INDICATOR',
    CAST(N'['AS nvarchar(max))
--         230329 IFP_23_RL02_NBUW_7954 modified start
--        {"code":null,"engDesc":"Not applicable","chinDesc":"","attributes":[{"nbwbCode":null,"eposCode":null}]}'
--        ,{"code":"Y","engDesc":"Y","chinDesc":"","attributes":[{"nbwbCode":"Y","eposCode":"Y"}]}'
--        ,{"code":"N","engDesc":"N","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":"N"}]}'
+N'        {"code":null,"engDesc":"Not Applicable","chinDesc":"","attributes":[{"nbwbCode":null,"eposCode":null}]}'
+N'        ,{"code":"Y","engDesc":"Y (eContract Only)","chinDesc":"","attributes":[{"nbwbCode":"Y","eposCode":"Y"}]}'
+N'        ,{"code":"N","engDesc":"N (Paper Contract)","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":"N"}]}'
--         230329 IFP_23_RL02_NBUW_7954 modified end
+N'    ]',
    '23-Apr-2023',
    NULL,
    'SYSTEM',
    '23-Apr-2023'
);
-->> 221116 IFP_23_RL01_NBUW_2450 modified end
commit;
