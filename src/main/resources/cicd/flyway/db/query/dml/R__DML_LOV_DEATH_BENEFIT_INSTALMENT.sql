--------------------------------------------------------------------------------------------
-- Mod. Date : 27 Feb 2024
-- Mod. By   : (MITDC CD) <PERSON> Hu
-- Mod. ref  : IFP_24_RL02_NBUW_14110_BE_B
-- Mod. Desc : 2024 Apr Release - 2024 New Product - Genesis (NBUWS)
-------------------------------------------------------------------------------------------
-- Mod. Date : 14 Mar 2024
-- Mod. By   : (MITDC CD) <PERSON> Wang
-- Mod. ref  : IFP_24_RL02_NBUW_14146_I
-- Mod. Desc : 2024 Apr Release - Support WIOP3 in NB2.0
-------------------------------------------------------------------------------------------
-- Mod. Date : 4 Jun 2024
-- Mod. By   : (MITDC CD) Ives Wang
-- Mod. ref  : IFP_24_RL03_NBUW_21953
-- Mod. Desc : 2024 Jul Release - Enable DBSO Fields for More Products
-------------------------------------------------------------------------------------------

--delete from LIST_OF_VALUE where LIST_CODE = 'DEATH_BENEFIT_INSTALMENT_IN_SCOPE_PRODUCT';
--
--insert into list_of_value values(
--    'DEATH_BENEFIT_INSTALMENT_IN_SCOPE_PRODUCT',
--    CAST(N'[' AS nvarchar(max))
--        +N'{"code":"PU500|WB012400","engDesc":"Genesis, Single Premium","chinDesc":"","attributes":[{}]}'
--        +N',{"code":"PU500|WB032400","engDesc":"Genesis, Paid Up In 3 Years","chinDesc":"","attributes":[{}]}'
--        +N',{"code":"PU500|WB052400","engDesc":"Genesis, Paid Up In 5 Years","chinDesc":"","attributes":[{}]}'
--        +N',{"code":"PU500|WB102400","engDesc":"Genesis, Paid Up In 10 Years","chinDesc":"","attributes":[{}]}'
--        +N',{"code":"PU500|WB152400","engDesc":"Genesis, Paid Up In 15 Years","chinDesc":"","attributes":[{}]}'
--        --IFP_24_RL02_NBUW_14146_I start
--        +N',{"code":"UK501","engDesc":"Whole-in-One Prime 3, Single Premium","chinDesc":"","attributes":[{}]}'
--        +N',{"code":"UK505","engDesc":"Whole-in-One Prime 3, Paid Up In 5 Years","chinDesc":"","attributes":[{}]}'
--        +N',{"code":"UK510","engDesc":"Whole-in-One Prime 3, Paid Up In 10 Years","chinDesc":"","attributes":[{}]}'
--        --IFP_24_RL02_NBUW_14146_I end
--     +N']',
--     '21-Apr-2024',
--     NULL,
--     'SYSTEM',
--     '21-Apr-2024'
--);
--
--commit;
