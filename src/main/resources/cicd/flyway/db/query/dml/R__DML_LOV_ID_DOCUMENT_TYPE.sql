------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------
-- Mod. Date : 28 Jun 2023
-- Mod. By   : (MITDC CD) <PERSON> Wang
-- Mod. ref  : IFP_23_RL04_NBUW_7367
-- Mod. Desc : [NB2.0][CDM][IT] NBUWS Customer Tab
-------------------------------------------------------------------------------------------
-- Mod. Date : 12 Dev 2023
-- Mod. By   : (MITDC CD) Everrett Xiang
-- Mod. ref  : IFP_24_RL01_NBUW_13074
-- Mod. Desc : [NB2.0][CDM][IT] Remove ID Type 'Passport'
-------------------------------------------------------------------------------------------
delete from LIST_OF_VALUE where LIST_CODE = 'ID_DOCUMENT_TYPE';

--IFP_23_RL04_NBUW_7367 update start
--insert into list_of_value values('ID_DOCUMENT_TYPE',N'[{"code":"2","engDesc":"HK Permanent Resident ID","chinDesc":"香港永久性居民身份證","attributes":[{"nbwbCode":"2","eposCode":"PERMANENT_HKID"}]}, {"code":"3","engDesc":"HK Non-Permanent Resident ID","chinDesc":"香港非永久居民身份證","attributes":[{"nbwbCode":"3","eposCode":"NON_PERMANENT_HKID"}]}, {"code":"4","engDesc":"Macau Resident ID","chinDesc":"澳門居民身份證","attributes":[{"nbwbCode":"4","eposCode":"MACAU_ID"}]}, {"code":"5","engDesc":"PRC Resident ID","chinDesc":"中國內地居民身份證","attributes":[{"nbwbCode":"5","eposCode":"PRC_ID"}]}, {"code":"8","engDesc":"USA Identity Documents","chinDesc":"美國身份證明文件","attributes":[{"nbwbCode":"8","eposCode":"USA_ID"}]}, {"code":"9","engDesc":"Passport","chinDesc":"護照","attributes":[{"nbwbCode":"9","eposCode":"PASSPORT"}]}, {"code":"6","engDesc":"Other ID","chinDesc":"其他身份證","attributes":[{"nbwbCode":"6","eposCode":"OTHER_ID"}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

--IFP_24_RL01_NBUW_13074 modified start
--insert into list_of_value values('ID_DOCUMENT_TYPE',N'[
--{"code":"2","engDesc":"HK Permanent Resident ID","chinDesc":"香港永久性居民身份證","attributes":[{"nbwbCode":"1","eposCode":"PERMANENT_HKID"}]},
--{"code":"3","engDesc":"HK Non-Permanent Resident ID","chinDesc":"香港非永久居民身份證","attributes":[{"nbwbCode":"1","eposCode":"NON_PERMANENT_HKID"}]},
--{"code":"4","engDesc":"Macau Resident ID","chinDesc":"澳門居民身份證","attributes":[{"nbwbCode":"4","eposCode":"MACAU_ID"}]},
--{"code":"5","engDesc":"PRC Resident ID","chinDesc":"中國內地居民身份證","attributes":[{"nbwbCode":"5","eposCode":"PRC_ID"}]},
--{"code":"8","engDesc":"USA Identity Documents","chinDesc":"美國身份證明文件","attributes":[{"nbwbCode":"8","eposCode":"USA_ID"}]},
--{"code":"9","engDesc":"Passport","chinDesc":"護照","attributes":[{"nbwbCode":"6","eposCode":"PASSPORT"}]},
--{"code":"6","engDesc":"Other ID","chinDesc":"其他身份證","attributes":[{"nbwbCode":"6","eposCode":"OTHER_ID"}]}
--,{"code":"7","engDesc":"N/A","chinDesc":"N/A","attributes":[{"nbwbCode":"7","eposCode":null}]}
--]',
--'24-Jul-2022',NULL,'SYSTEM','24-Jul-2022');
--IFP_24_RL01_NBUW_13074 modified end

--IFP_24_RL01_NBUW_13074 added start
insert into LIST_OF_VALUE values(
    'ID_DOCUMENT_TYPE',
    CAST(N'[' AS nvarchar(max))
        +N'{"code":"2","engDesc":"HK Permanent Resident ID","chinDesc":"香港永久性居民身份證","attributes":[{"nbwbCode":"1","eposCode":"PERMANENT_HKID"}]}'
        +N',{"code":"3","engDesc":"HK Non-Permanent Resident ID","chinDesc":"香港非永久居民身份證","attributes":[{"nbwbCode":"1","eposCode":"NON_PERMANENT_HKID"}]}'
        +N',{"code":"4","engDesc":"Macau Resident ID","chinDesc":"澳門居民身份證","attributes":[{"nbwbCode":"4","eposCode":"MACAU_ID"}]}'
        +N',{"code":"5","engDesc":"PRC Resident ID","chinDesc":"中國內地居民身份證","attributes":[{"nbwbCode":"5","eposCode":"PRC_ID"}]}'
        +N',{"code":"8","engDesc":"USA Identity Documents","chinDesc":"美國身份證明文件","attributes":[{"nbwbCode":"8","eposCode":"USA_ID"}]}'
        +N',{"code":"6","engDesc":"Other ID","chinDesc":"其他身份證","attributes":[{"nbwbCode":"6","eposCode":"OTHER_ID"}]}'
        +N',{"code":"7","engDesc":"N/A","chinDesc":"N/A","attributes":[{"nbwbCode":"7","eposCode":null}]}'
    +N']',
    '07-Jan-2024',
    NULL,
	'SYSTEM',
	'07-Jan-2024');
--IFP_24_RL01_NBUW_13074 added end

commit;

