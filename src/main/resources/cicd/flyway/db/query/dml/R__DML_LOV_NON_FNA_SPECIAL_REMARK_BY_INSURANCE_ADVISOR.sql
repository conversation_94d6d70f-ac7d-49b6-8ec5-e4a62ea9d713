-- Mod. Date : 08 Aug 2023
-- Mod. By   : Cathy Zhao
-- Mod. ref  : IFP_23_RL05_NBUW_10654
-- Mod. Desc : Add fields (and checking) in NB2.0 for NB app form buying objective checking for FNA exempted products if partial match
-------------------------------------------------------------------------------------------
--IFP_23_RL05_NBUW_10654 start
delete from LIST_OF_VALUE where LIST_CODE = 'NON_FNA_SPECIAL_REMARK_BY_INSURANCE_ADVISOR';

insert into list_of_value values('NON_FNA_SPECIAL_REMARK_BY_INSURANCE_ADVISOR',N'[{"code":"A","engDesc":"Applicant understands the product only meet with financial protection against adversities","chinDesc":"申请人明白本产品只提供财务上的保护","attributes":[{"nbwbCode":"A","eposCode":"A"}]},{"code":"O","engDesc":"Others","chinDesc":"其他","attributes":[{"nbwbCode":"O","eposCode":"O"}]}]','08-Aug-2023',NULL,'SYSTEM','08-Aug-2023');

--IFP_23_RL05_NBUW_10654 end
commit;
