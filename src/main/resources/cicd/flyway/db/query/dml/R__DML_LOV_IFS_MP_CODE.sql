------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

delete from LIST_OF_VALUE where LIST_CODE = 'IFS_MP_CODE';

insert into list_of_value values('IFS_MP_CODE',N'[{"code":"O","engDesc":"Outstanding","chinDesc":"","attributes":[{"nbwbCode":"O","eposCode":""}]}, {"code":"N","engDesc":"Not Applicable","chinDesc":"","attributes":[{"nbwbCode":"N","eposCode":""}]}, {"code":"S","engDesc":"Submitted","chinDesc":"","attributes":[{"nbwbCode":"S","eposCode":""}]}]','08-NOV-2020',NULL,'SYSTEM','08-NOV-2020');

commit;
