-------------------------------------------------------------------------------------------
-- Mod. Date : 28 Feb 2022
-- Mod. By   : (MITDC CD) <PERSON>
-- Mod. ref  : IFP_22_RL04_JMIFUM_845
-- Mod. Desc : 2022 Apr Release - Manuli<PERSON> Supreme VHIS Flexi Plan - Repricing
-------------------------------------------------------------------------------------------
-- Mod. Date : 22 Mar 2022
-- Mod. By   : (MITDC CD) <PERSON> Pan
-- Mod. ref  : IFP_22_RL04_NBUW_308
-- Mod. Desc : 2022 Apr Release - <PERSON><PERSON>fe Supreme VHIS Flexi Plan - Repricing - Error message display
-------------------------------------------------------------------------------------------
-- Mod. Date : 07 Sep 2023
-- Mod. By   : (MITDC CD) Joan Zhao
-- Mod. ref  : IFP_23_RL05_NBUW_11326
-- Mod. Desc : 2023 Nov Release - Manulife Supreme VHIS Flexi Plan - Repricing
-------------------------------------------------------------------------------------------
delete from SYS_CONFIG where CONFIG_CODE = 'CHK_CVG_EFF_DATE';



--IFP_22_RL04_JMIFUM_845 start
--insert into sys_config values('CHK_CVG_EFF_DATE','[{"planCode":"HS599","schmCode":null,"coverageClass":"C1D","effectiveDate":"2022-06-01"}]','28-Feb-2022','SYSTEM','28-Feb-2022');
--IFP_22_RL04_JMIFUM_845 end
--IFP_23_RL05_NBUW_11326 start
--IFP_22_RL04_NBUW_308 start
--insert into sys_config values('CHK_CVG_EFF_DATE','[{"planCode":"HS599","schmCode":null,"coverageClass":"C1D","effectiveDate":"01-Jun-2022"}]','28-Feb-2022','SYSTEM','03-Apr-2022');
--IFP_22_RL04_NBUW_308 end
insert into sys_config values('CHK_CVG_EFF_DATE',
                              N'[{"planCode":"HS599","schmCode":null,"coverageClass":"C1D","effectiveDate":"01-Jun-2022"}'
                              +N',{"planCode":"HS599","schmCode":null,"coverageClass":"C1E","effectiveDate":"01-Jan-2024"}'
                              +N',{"planCode":"HS599","schmCode":null,"coverageClass":"D1A","effectiveDate":"01-Jan-2024"}'
                              +N',{"planCode":"HS599","schmCode":null,"coverageClass":"D1C","effectiveDate":"01-Jan-2024"}'
                              +N',{"planCode":"HS599","schmCode":null,"coverageClass":"D1D","effectiveDate":"01-Jan-2024"}'
                              +N',{"planCode":"HS599","schmCode":null,"coverageClass":"D1E","effectiveDate":"01-Jan-2024"}'
                              +N']',
                              '28-Feb-2022',
                              'SYSTEM',
                              '06-Nov-2023');
--IFP_23_RL05_NBUW_11326 end
commit;
