------------------------------------------------------------------------------------------
-- Mod. Date : 20 Mar 2020
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_FATCA](
	[POLICY_NO] [NVARCHAR](20) NOT NULL,
	[JSON_CONTEXT] [NVARCHA<PERSON>](MAX) NOT NULL,
	[CREATED_TIMESTAMP] [D<PERSON><PERSON><PERSON>E] NOT NULL,
	[CREATED_BY] [VA<PERSON><PERSON>](50) NOT NULL,
	[LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
	[LAST_UPDATED_BY] [VARCHAR](50) NULL,
	[DELETED_TIMESTAMP] [DATETIME] NULL,
	[DELETED_BY] [VARCHAR](50) NULL,
 CONSTRAINT [PK_NB_FATCA] PRIMARY KEY CLUSTERED 
(
	[POLICY_NO] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO