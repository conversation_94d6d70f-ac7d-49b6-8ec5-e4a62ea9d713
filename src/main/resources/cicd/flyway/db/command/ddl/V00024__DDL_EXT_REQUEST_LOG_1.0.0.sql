------------------------------------------------------------------------------------------
-- Mod. Date : 2 Mar 2020
-- Mod. By   : Vimal V
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[EXT_REQUEST_LOG](
	[REQ_ID] [NUMERIC](15) NOT NULL,
	[REQ_DATE] [DATETIME] NULL,
	[POLICY_NO] [NVARCHAR](20) NULL,
	[TRACE_ID] [NVARCHAR](100) NULL,
	[REQUEST_URL] [NVARCHAR](MAX) NULL,
	[CONFIG_KEY] [NVARCHAR](1000) NULL,
	[REQUEST] [NVARCHAR](MAX) NULL,
	[RESPONSE] [NVARCHAR](MAX) NULL,
	[STATUS] [NUMERIC](10) NULL,
	[ELAPSED_TIME] [NUMERIC](15) NULL,
 CONSTRAINT [PK_EXT_REQUEST_LOG] PRIMARY KEY CLUSTERED 
(
	[REQ_ID] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY],
 INDEX [IDX_ext_req_log_traceId] NONCLUSTERED 
 (
	[TRACE_ID] ASC,
	[REQ_DATE] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY],
 INDEX [IDX_ext_req_log_pol] NONCLUSTERED 
 (
	[POLICY_NO] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE SEQUENCE [dbo].[SEQ_EXT_REQUEST_LOG] AS [NUMERIC](10) START WITH 1 INCREMENT BY 1 CACHE 50
GO