------------------------------------------------------------------------------------------
-- Mod. Date : 20 Mar 2020
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : API to determine STP result
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[FIELD_CHANGE](
	[CHG_ID] [NUMERIC](16) NOT NULL,
	[POLICY_NO] [NVARCHAR](20) NOT NULL,
	[NODE_NAME] [NVARCHAR](100) NOT NULL,
	[FIELD_NAME] [NVARCHAR](100) NOT NULL,
	[NEW_VALUE] [NVARCHAR](MAX) NOT NULL,
	[OLD_VALUE] [NVARCHAR](MAX) NOT NULL,
	[CREATED_TIMESTAMP] [DATETIME] NOT NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL,
 CONSTRAINT [PK_FIELD_CHANGE] PRIMARY KEY CLUSTERED 
(
	[CHG_ID] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO

CREATE SEQUENCE [dbo].[SEQ_FIELD_CHANGE] AS [NUMERIC](15) START WITH 0 INCREMENT BY 1 CACHE 50
GO