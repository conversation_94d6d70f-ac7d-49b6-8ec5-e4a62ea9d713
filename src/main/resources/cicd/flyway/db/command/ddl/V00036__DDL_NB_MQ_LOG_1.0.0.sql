------------------------------------------------------------------------------------------
-- Mod. Date : 29 Jun 2023
-- Mod. By   : (MITDC CD) Lane Zeng
-- Mod. ref  : IFP_23_RL03_HIU_508
-- Mod. Desc : 2023 Jul Release - RabbitMQ Migration Azure Service Bus
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_MQ_LOG](
    [POLICY_NO] [NVARCHAR](20) NOT NULL,
    [MSG_ID] [NVARCHAR](50) NOT NULL,
    [MSG_CONTEXT] [NVARCHAR](MAX) NOT NULL,
    [MSG_TOPIC] [NVARCHAR](100) NOT NULL,
    [MSG_NAMESPACE] [NVARCHAR](100) NOT NULL,
    [SOURCE_SYS] [NVARCHAR](50) NOT NULL,
    [IS_PUBLISHED] [NVARCHAR](50) NOT NULL,
    [CREATED_TIMESTAMP] [DATETIME] NOT NULL,
    [CREATED_BY] [VARCHAR](50) NOT NULL,
    [LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
    [LAST_UPDATED_BY] [VARCHAR](50) NULL,
    [DELETED_TIMESTAMP] [DATETIME] NULL,
    [DELETED_BY] [VARCHAR](50) NULL,
 CONSTRAINT [PK_NB_MQ_LOG] PRIMARY KEY CLUSTERED
(
    [MSG_ID] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO