------------------------------------------------------------------------------------------
-- Mod. Date : 02 Feb 2020
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : Initial Development
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[SagaEntry](
	[sagaId] [nvarchar](255) NOT NULL,
	[revision] [nvarchar](255) NULL,
	[sagaType] [nvarchar](255) NULL,
	[serializedSaga] [image] NULL,
PRIMARY KEY CLUSTERED 
(
	[sagaId] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK_SagaEntry_2] UNIQUE NONCLUSTERED 
(
	[sagaId] ASC,
	[sagaType] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO