------------------------------------------------------------------------------------------
-- Mod. Date : 03 Sep 2021
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_21_RL05_SB_NB_22194
-- Mod. Desc : NB Rule Result
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [DBO].[NB_RULE_RESULT](
	[POLICY_NO] [NVARCHAR](20) NOT NULL,
	[JSON_CONTEXT] [NVARCHAR](MAX) NOT NULL,
	[CREATED_TIMESTAMP] [DATETIME] NOT NULL,
	[CREATED_BY] [VARCHAR](50) NOT NULL,
	[LAST_UPDATED_TIMESTAMP] [DATETIME] NULL,
	[LAST_UPDATED_BY] [VARCHAR](50) NULL,
	[DELETED_TIMESTAMP] [DATETIME] NULL,
	[DELETED_BY] [VARCHAR](50) NULL,
 CONSTRAINT [PK_NB_RULE_RESULT] PRIMARY KEY CLUSTERED 
(
	[POLICY_NO] ASC
)WITH (PAD_INDEX = OFF,STATISTICS_NORECOMPUTE = OFF,IGNORE_DUP_KEY = OFF,ALLOW_ROW_LOCKS = ON,ALLOW_PAGE_LOCKS = ON) ON [PRIMARY]
) ON [PRIMARY]
GO