------------------------------------------------------------------------------------------
-- Mod. Date : 02 Feb 2020
-- Mod. By   : <PERSON>
-- Mod. ref  : IFP_20_RL04_SB_NB_10956
-- Mod. Desc : Initial Development
-------------------------------------------------------------------------------------------

SET ANSI_NULLS ON
GO

SET QUOTED_IDENTIFIER ON
GO

CREATE TABLE [dbo].[DomainEventEntry](
	[globalIndex] [numeric](19, 0) NOT NULL,
	[eventIdentifier] [nvarchar](255) NOT NULL,
	[metaData] [image] NULL,
	[payload] [image] NOT NULL,
	[payloadRevision] [nvarchar](255) NULL,
	[payloadType] [nvarchar](255) NOT NULL,
	[timeStamp] [varchar](255) NOT NULL,
	[aggregateIdentifier] [nvarchar](255) NOT NULL,
	[sequenceNumber] [numeric](19, 0) NOT NULL,
	[type] [varchar](255) NULL,
PRIMARY KEY CLUSTERED 
(
	[globalIndex] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK_DomainEventEntry_1] UNIQUE NONCLUSTERED 
(
	[eventIdentifier] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY],
 CONSTRAINT [UK_DomainEventEntry_2] UNIQUE NONCLUSTERED 
(
	[aggregateIdentifier] ASC,
	[sequenceNumber] ASC
)WITH (STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF) ON [PRIMARY]
) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]
GO