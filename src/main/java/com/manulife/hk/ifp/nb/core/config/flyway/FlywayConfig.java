/*
 * FlywayConfig
 *
 * Copyright (c) 2024 Manulife International Ltd.
 *
 * Description:
 *
 *
 * Maintenance History
 *
 * YYMMDD Who              Reason
 * ====== ================ ==================================================================================================================================
 * 240623 (MITDC CD) Auter Zhang         IFP_24_RL03_NBUW_23992 - setup flyway pipeline
 */

//240623 IFP_24_RL03_NBUW_23992 commented start
//package com.manulife.hk.ifp.nb.core.config.flyway;
//
//import javax.sql.DataSource;
//
//import org.flywaydb.core.Flyway;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//
//@Configuration
//public class FlywayConfig {
//
//	@Bean(name = "flyway")
//	public Flyway commandFlyway(@Autowired @Qualifier("commandDataSource") DataSource commandDataSource,
//			@Autowired FlywayCommandDataSourceProperties flywayCommandDataSourceProperties) {
//		Flyway commandFlyway = Flyway.configure().dataSource(commandDataSource).baselineOnMigrate(true)
//				.locations(flywayCommandDataSourceProperties.getLocation()).load();
//		commandFlyway.migrate();
//		return commandFlyway;
//	}
//
//
//	@Bean(name = "queryFlyway")
//	public Flyway queryFlyway(@Autowired @Qualifier("queryDataSource") DataSource queryDataSource,
//			@Autowired FlywayQueryDataSourceProperties flywayQueryDataSourceProperties) {
//		Flyway queryFlyway = Flyway.configure().dataSource(queryDataSource).baselineOnMigrate(true)
//				.locations(flywayQueryDataSourceProperties.getLocation()).load();
//		queryFlyway.migrate();
//		return queryFlyway;
//	}
//}
//240623 IFP_24_RL03_NBUW_23992 commented end
